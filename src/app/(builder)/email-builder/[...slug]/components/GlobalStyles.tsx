import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>uttonsContainer } from "@components/builder/TabButton";
import { ToggleSection } from "@components/builder/ToggleSection";
import EmailStyles from "./global-styles/EmailStyles";
import TextStyles from "./global-styles/TextStyles";
import TextMobileStyles from "./global-styles/TextMobileStyles";
import ButtonStyles from "./global-styles/ButtonStyles";
import DividerStyles from "./global-styles/DividerStyles";
import LinkStyles from "./global-styles/LinkStyles";
import useBuilderStore from "@stores/builder/useBuilderStore";
import { Separator } from "@components/shadcn/Separator";

const GlobalStyles = () => {
  const { setViewMode, viewMode, focusedItem } = useBuilderStore();
  const [activeSection, setActiveSection] = useState<string | null>("email");
  const { activeSidebarOption } = useBuilderStore();
  const isVisible = !focusedItem && activeSidebarOption === "styles";

  const desktopSections = [
    { id: "email", title: "Email" },
    { id: "text", title: "Text" },
    { id: "link", title: "Link" },
    { id: "button", title: "Button" },
    { id: "divider", title: "Divider" },
  ];

  const mobileSections = [{ id: "text", title: "Text" }];

  const onClick = (id: string) => {
    if (activeSection === id) {
      setActiveSection(null);
    } else {
      setActiveSection(id);
    }
  };

  if (!isVisible) return null;
  return (
    <div className="flex w-[330px] flex-shrink-0 flex-col border-gray-200 bg-white">
      <TabButtonsContainer>
        <TabButton name="Desktop" isActive={viewMode === "desktop"} onClick={() => setViewMode("desktop")} />
        <TabButton name="Mobile" isActive={viewMode === "mobile"} onClick={() => setViewMode("mobile")} />
      </TabButtonsContainer>
      <div className="flex w-full flex-col overflow-y-auto px-5">
        {viewMode === "desktop" &&
          desktopSections.map((s, i) => (
            <div key={s.id}>
              <ToggleSection
                key={s.id}
                title={s.title}
                isActive={activeSection === s.id}
                onClick={() => onClick(s.id)}
              >
                {s.id === "email" && <EmailStyles />}
                {s.id === "text" && <TextStyles />}
                {s.id === "link" && <LinkStyles />}
                {s.id === "button" && <ButtonStyles />}
                {s.id === "divider" && <DividerStyles />}
              </ToggleSection>
              <Separator />
            </div>
          ))}
        {viewMode === "mobile" &&
          mobileSections.map((s, i) => (
            <div key={s.id}>
              <ToggleSection
                key={s.id}
                title={s.title}
                isActive={activeSection === s.id}
                onClick={() => onClick(s.id)}
              >
                {s.id === "text" && <TextMobileStyles />}
              </ToggleSection>
              <Separator />
            </div>
          ))}
      </div>
    </div>
  );
};

export default GlobalStyles;
