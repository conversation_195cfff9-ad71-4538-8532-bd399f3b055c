import useBuilderStore from "@stores/builder/useBuilderStore";
import useSaveTemplate from "@hooks/builder/useSaveTemplate";
import UndoIcon from "mdi-react/UndoIcon";
import RedoIcon from "mdi-react/RedoIcon";
import ProgressUploadIcon from "mdi-react/ProgressUploadIcon";
import SendTestEmailDialog from "./SendTestEmailDialog";
import LoadingCircle from "@components/primitive/LoadingCircle";
import HeaderLogo from "@components/header/HeaderLogo";
import Button from "@components/primitive/Button";
import Tooltip from "@components/primitive/Tooltip";
import PreviewDrawer from "./PreviewDrawer";
import { useLoadTemplate } from "@hooks/builder/useLoadTemplate";
import useHistoryManager from "@hooks/builder/useHistoryManager";
import { useRouter } from "next/navigation";
import { usePathParams } from "@hooks/builder/usePathParams";
import useEventEmailCampaign from "@hooks/useEventEmailCampaign";

interface Props {
  userId: string;
}

const Navbar = ({ userId }: Props) => {
  useLoadTemplate();
  const { undo, redo } = useHistoryManager();
  const { setIsPreviewOpen, setIsTestEmailOpen, isPreviewOpen, savingStatus } = useBuilderStore();
  const { save } = useSaveTemplate();
  const router = useRouter();
  const { eventId, emailCampaignId } = usePathParams();
  const { eventEmailCampaign } = useEventEmailCampaign({ eventId, emailCampaignId });
  const campaignStatus = eventEmailCampaign?.status;
  const campaignName = eventEmailCampaign?.name || "";
  const canSave = campaignStatus === "disapproved" || campaignStatus === "draft";
  //const { getEmailHtml } = useEmailHtml();
  //const emailHtml = getEmailHtml();

  const goBack = () => {
    router.replace(`/manage-event-emails/${eventId}`);
  };

  return (
    <>
      <nav className="sticky z-[10] flex h-[64px] items-center justify-between border-b border-light300 bg-light100 px-6 lg:px-8">
        <div className="mx-auto flex w-full max-w-screen-2xl items-center justify-between px-4">
          <div className="flex items-center space-x-3">
            <Tooltip label="Dashboard" side="left">
              <button onClick={goBack}>
                <HeaderLogo />
              </button>
            </Tooltip>
            <p>{campaignName}</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center overflow-hidden rounded-md border bg-gray-50 text-sm text-gray-500">
              <div className="border-r">
                <Tooltip label="Save" side="left">
                  <button
                    aria-label="Save"
                    disabled={!canSave || !!savingStatus}
                    onClick={canSave ? () => save("manual") : undefined}
                    className="p-2 transition-colors hover:bg-light200"
                  >
                    {!!savingStatus ? (
                      <LoadingCircle size="xs" />
                    ) : (
                      <ProgressUploadIcon className="h-5 w-5" />
                    )}
                  </button>
                </Tooltip>
              </div>
              <div className="flex items-center">
                <Tooltip label="Undo" side="left">
                  <button
                    onClick={() => undo()}
                    className="border-r p-2 transition-colors hover:bg-light200"
                    aria-label="Undo"
                  >
                    <UndoIcon className="h-5 w-5" />
                  </button>
                </Tooltip>
                <Tooltip label="Redo" side="left">
                  <button
                    onClick={() => redo()}
                    className="p-2 transition-colors hover:bg-light200"
                    aria-label="Redo"
                  >
                    <RedoIcon className="h-5 w-5" />
                  </button>
                </Tooltip>
              </div>
            </div>
            <Button text="Preview" variant="outlineLight" size="sm" onClick={() => setIsPreviewOpen(true)} />
            <Button
              size="sm"
              onClick={() => setIsTestEmailOpen(true)}
              text="Send test"
              variant="outlineLight"
            />
            <Button
              onClick={canSave ? () => save("save-exit") : undefined}
              text={savingStatus === "save-exit" ? "Saving..." : "Save and exit"}
              loading={savingStatus === "save-exit"}
              disabled={!canSave || !!savingStatus}
              size="sm"
            />
          </div>
        </div>
      </nav>
      <SendTestEmailDialog userId={userId} />
      {isPreviewOpen && <PreviewDrawer />}
    </>
  );
};

export default Navbar;
