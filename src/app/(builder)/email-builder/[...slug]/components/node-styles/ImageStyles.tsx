import { useState } from "react";
import Image from "next/image";
import { TabButtonsGroup, TabButton } from "@components/builder/TabButtons";
import { Input } from "@components/shadcn/Input";
import Button from "@components/primitive/Button";
import { Checkbox } from "@components/shadcn/Checkbox";
import { toast } from "sonner";
import ImageIcon from "mdi-react/ImageIcon";
import { CustomNumberInput } from "@components/primitive/CustomNumberInput";
import StylesSection from "@components/builder/StylesSection";
import StylesSectionHeader from "@components/builder/StylesSectionHeader";
import Label from "@components/builder/Label";
import ColorPickerButton from "@components/builder/ColorPickerButton";
import useBuilderStore from "@stores/builder/useBuilderStore";
import { useEmailBuilderContext } from "@components/builder/context";
import useUploadAddSingleMedia from "@hooks/useUploadAddSingleMedia";
import useComponentAttributes from "@hooks/builder/useComponentAttributes";
import newId from "@lib/newId";
import Slider from "@components/primitive/Slider";
import useGlobalStylesStore from "@stores/builder/useGlobalStylesStore";
import MobileStyleToggle from "@components/builder/MobileStyleToggle";
import { Switch } from "@components/shadcn/Switch";

const uploadType = "event";

const ImageStyles = () => {
  const { imageStyle } = useGlobalStylesStore();
  const { setLocalColorPicker } = useBuilderStore();
  const { updateComponentAttrs } = useComponentAttributes();
  const { editorRef } = useEmailBuilderContext();
  const editor = editorRef.current;
  const attributes = editor?.getAttributes("image");
  const [alignMobileChecked, setAlignMobileChecked] = useState(false);
  const [adaptImage, setAdaptImage] = useState(attributes?.adaptImage ?? false);
  const { upload, uploadInfo } = useUploadAddSingleMedia({ uploadType });
  const { progress, uploading } = uploadInfo;
  const sizeMode = attributes?.sizeMode ?? imageStyle.sizeMode;
  const alignDesktop = attributes?.align ?? imageStyle.align;
  const alignMobile = attributes?.alignMobile ?? "";
  const align = alignMobileChecked ? alignMobile : alignDesktop;
  const paddingApplyToAll = attributes?.paddingApplyToAll ?? imageStyle.paddingApplyToAll;

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      const item: UploadMediaItem = {
        fileName: file.name,
        height: 0,
        width: 0,
        uri: URL.createObjectURL(file),
        id: newId(),
        mediaId: "",
        type: "image",
        source: "uploaded",
        url: "",
      };
      const uploadedItem = await upload(item);
      if (!uploadedItem) return;
      const url = uploadedItem.url;
      editor?.chain().focus().updateAttributes("image", { src: url }).run();
    } catch (error) {
      console.error("Upload error:", error);
      toast.error(error instanceof Error ? error.message : "Failed to upload image");
    }
  };

  const updatePaddingTop = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "paddingTop", value }],
      component: "image",
    });
  };

  const updatePaddingBottom = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "paddingBottom", value }],
      component: "image",
    });
  };

  const updatePaddingLeft = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "paddingLeft", value }],
      component: "image",
    });
  };

  const updatePaddingRight = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "paddingRight", value }],
      component: "image",
    });
  };

  const updateAllPadding = (value: number) => {
    updateComponentAttrs({
      updates: [
        { attribute: "paddingTop", value },
        { attribute: "paddingBottom", value },
        { attribute: "paddingLeft", value },
        { attribute: "paddingRight", value },
      ],
      component: "image",
    });
  };

  const handleApplyPaddingToAll = (checked: boolean) => {
    if (!checked) {
      updateComponentAttrs({
        updates: [{ attribute: "paddingApplyToAll", value: checked }],
        component: "image",
      });
    }
    const value = attributes?.paddingTop ?? imageStyle.paddingTop;
    updateComponentAttrs({
      updates: [
        { attribute: "paddingTop", value },
        { attribute: "paddingBottom", value },
        { attribute: "paddingLeft", value },
        { attribute: "paddingRight", value },
        { attribute: "paddingApplyToAll", value: checked },
      ],
      component: "image",
    });
  };

  const updateBorderRadius = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "borderRadius", value }],
      component: "image",
    });
  };

  const updateAltText = (value: string) => {
    updateComponentAttrs({
      updates: [{ attribute: "alt", value }],
      component: "image",
    });
  };

  const toggleOriginalSize = () => {
    updateComponentAttrs({
      updates: [{ attribute: "sizeMode", value: "original" }],
      component: "image",
    });
  };

  const toggleFillSize = () => {
    updateComponentAttrs({
      updates: [{ attribute: "sizeMode", value: "fill" }],
      component: "image",
    });
  };

  const toggleScaleSize = () => {
    updateComponentAttrs({
      updates: [
        { attribute: "sizeMode", value: "scale" },
        { attribute: "width", value: 100 },
      ],
      component: "image",
    });
  };

  const updateAlignment = (value: "left" | "center" | "right") => {
    if (alignMobileChecked) {
      updateComponentAttrs({
        updates: [{ attribute: "alignMobile", value }],
        component: "image",
      });
      return;
    }

    updateComponentAttrs({
      updates: [{ attribute: "align", value }],
      component: "image",
    });
  };

  const updateImageScale = (values: number[]) => {
    const value = values[0];
    updateComponentAttrs({
      updates: [{ attribute: "width", value }],
      component: "image",
    });
  };

  const removeImage = () => {
    updateComponentAttrs({
      updates: [{ attribute: "src", value: "" }],
      component: "image",
    });
  };

  const handleAdaptImage = (checked: boolean) => {
    setAdaptImage(checked);
    updateComponentAttrs({
      updates: [{ attribute: "adaptImage", value: checked }],
      component: "image",
    });
  };

  return (
    <>
      <StylesSection>
        <div className="relative flex h-[200px] w-full flex-col items-center justify-center rounded-md border border-light200 bg-white">
          {attributes?.src ? (
            <Image
              src={attributes?.src}
              alt={attributes?.alt}
              className="h-[200px] w-full rounded-md object-cover object-top"
              height={200}
              width={380}
            />
          ) : (
            <ImageIcon className="h-12 w-12 text-light400" />
          )}
        </div>
        <div className="flex gap-2">
          <Input
            type="file"
            accept="image/png, image/jpeg, image/jpg"
            onChange={handleImageUpload}
            className="hidden"
            id="image-replace"
            disabled={uploading}
          />
          <Button
            disabled={uploading}
            fullWidth
            loading={uploading}
            onClick={() => document.getElementById("image-replace")?.click()}
            size="sm"
            text={uploading ? "Uploading" : !attributes?.src ? "Upload" : "Replace"}
            variant="outlineLight"
          />
          {attributes?.src && (
            <Button
              fullWidth
              loading={false}
              onClick={removeImage}
              size="sm"
              text="Remove"
              variant="outlineLight"
            />
          )}
        </div>
      </StylesSection>
      <StylesSection>
        <StylesSectionHeader>Size</StylesSectionHeader>
        <TabButtonsGroup
          buttons={
            <>
              <TabButton onClick={toggleOriginalSize} isActive={sizeMode === "original"} label="Original" />
              <TabButton onClick={toggleFillSize} isActive={sizeMode === "fill"} label="Fill" />
              <TabButton onClick={toggleScaleSize} isActive={sizeMode === "scale"} label="Scale" />
            </>
          }
        >
          {sizeMode === "scale" && (
            <div className="space-y-2 rounded-b-lg bg-white px-4 py-4">
              <div className="flex items-center justify-between gap-4">
                <Slider
                  value={[attributes?.width ?? imageStyle.width]}
                  defaultValue={[1]}
                  onValueChange={updateImageScale}
                  min={0}
                  max={100}
                  step={1}
                />
                <Label>{attributes?.width ?? imageStyle.width}%</Label>
              </div>
            </div>
          )}
        </TabButtonsGroup>
      </StylesSection>
      <StylesSection>
        <div className="flex items-center justify-between">
          <StylesSectionHeader>Responsive Image</StylesSectionHeader>
          <Switch disabled={false} checked={adaptImage} onCheckedChange={handleAdaptImage} />
        </div>
        <Label>
          Adjust the image to the width of the screen of the mobile device. Not supported on all devices.
        </Label>
      </StylesSection>
      <StylesSection>
        <StylesSectionHeader>{alignMobileChecked ? "Mobile Alignment" : "Alignment"}</StylesSectionHeader>
        <div className="flex items-center justify-between gap-4">
          <TabButtonsGroup
            buttons={
              <>
                <TabButton onClick={() => updateAlignment("left")} isActive={align === "left"} label="Left" />
                <TabButton
                  onClick={() => updateAlignment("center")}
                  isActive={align === "center"}
                  label="Center"
                />
                <TabButton
                  onClick={() => updateAlignment("right")}
                  isActive={align === "right"}
                  label="Right"
                />
              </>
            }
          />
          <MobileStyleToggle
            checked={alignMobileChecked}
            onClick={() => setAlignMobileChecked((prev) => !prev)}
          />
        </div>
      </StylesSection>
      <StylesSection>
        <StylesSectionHeader>Alt Text</StylesSectionHeader>
        <Input
          value={attributes?.alt || ""}
          onChange={(e) => updateAltText(e.target.value)}
          placeholder="Describe the image"
        />
      </StylesSection>
      <StylesSection>
        <StylesSectionHeader>Border Radius</StylesSectionHeader>
        <CustomNumberInput
          value={attributes?.borderRadius ?? imageStyle.borderRadius}
          onChange={updateBorderRadius}
        />
      </StylesSection>
      <StylesSection>
        <StylesSectionHeader>Colors</StylesSectionHeader>
        <ColorPickerButton
          title="Block Background"
          color={attributes?.backgroundColor ?? imageStyle.backgroundColor}
          onClick={() =>
            setLocalColorPicker({
              attribute: "backgroundColor",
              component: "image",
            })
          }
        />
      </StylesSection>
      <StylesSection>
        <div className="flex items-center justify-between">
          <StylesSectionHeader>Padding</StylesSectionHeader>
          <div className="flex items-center gap-2">
            <Checkbox checked={paddingApplyToAll} onCheckedChange={handleApplyPaddingToAll} />
            <Label>Apply to all sides</Label>
          </div>
        </div>
        <div className={paddingApplyToAll ? "" : "grid grid-cols-2 gap-3"}>
          {paddingApplyToAll ? (
            <CustomNumberInput
              value={attributes?.paddingTop ?? imageStyle.paddingTop}
              onChange={updateAllPadding}
              className="h-8 w-full py-5"
            />
          ) : (
            <>
              <StylesSection>
                <Label>Top</Label>
                <CustomNumberInput
                  value={attributes?.paddingTop ?? imageStyle.paddingTop}
                  onChange={updatePaddingTop}
                  className="h-8 py-5"
                />
              </StylesSection>
              <StylesSection>
                <Label>Bottom</Label>
                <CustomNumberInput
                  value={attributes?.paddingBottom ?? imageStyle.paddingBottom}
                  onChange={updatePaddingBottom}
                />
              </StylesSection>
              <StylesSection>
                <Label>Left</Label>
                <CustomNumberInput
                  value={attributes?.paddingLeft ?? imageStyle.paddingLeft}
                  onChange={updatePaddingLeft}
                  className="h-8 py-5"
                />
              </StylesSection>
              <StylesSection>
                <Label>Right</Label>
                <CustomNumberInput
                  value={attributes?.paddingRight ?? imageStyle.paddingRight}
                  onChange={updatePaddingRight}
                  className="h-8 py-5"
                />
              </StylesSection>
            </>
          )}
        </div>
      </StylesSection>
    </>
  );
};

export default ImageStyles;
