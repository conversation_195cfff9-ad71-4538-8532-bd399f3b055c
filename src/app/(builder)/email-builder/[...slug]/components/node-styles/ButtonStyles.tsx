import { Input } from "@components/shadcn/Input";
import StylesSection from "@components/builder/StylesSection";
import { Checkbox } from "@components/shadcn/Checkbox";
import { Button } from "@components/shadcn/Button";
import { CustomNumberInput } from "@components/primitive/CustomNumberInput";
import StylesSectionHeader from "@components/builder/StylesSectionHeader";
import ColorPickerButton from "@components/builder/ColorPickerButton";
import Label from "@components/builder/Label";
import useBuilderStore from "@stores/builder/useBuilderStore";
import { useEmailBuilderContext } from "@components/builder/context";
import useComponentAttributes from "@hooks/builder/useComponentAttributes";
import { getLinkPrefix, getLinkType, isValidUrl } from "@lib/builder/link";
import useGlobalStylesStore from "@stores/builder/useGlobalStylesStore";
import { TabButtonsGroup, TabButton } from "@components/builder/TabButtons";
import { EMAIL_FONTS_MAP } from "@lib/builder/font";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/shadcn/Select";

const ButtonStyles = () => {
  const { buttonStyle } = useGlobalStylesStore();
  const { editorRef } = useEmailBuilderContext();
  const editor = editorRef.current;
  const { setLocalColorPicker } = useBuilderStore();
  const { updateComponentAttrs } = useComponentAttributes();
  const attributes = editor?.getAttributes("button");
  const href = attributes?.href || "";
  const linkType = getLinkType(href);
  const align = attributes?.align ?? buttonStyle.align;
  const internalPaddingApplyToAll =
    attributes?.internalPaddingApplyToAll ?? buttonStyle.internalPaddingApplyToAll;
  const externalPaddingApplyToAll =
    attributes?.externalPaddingApplyToAll ?? buttonStyle.externalPaddingApplyToAll;

  const toggleInternalPaddingToAll = (checked: boolean) => {
    if (!checked) {
      updateComponentAttrs({
        updates: [{ attribute: "internalPaddingApplyToAll", value: checked }],
        component: "button",
      });
      return;
    }

    const value = attributes?.internalPaddingTop ?? buttonStyle.internalPaddingTop;
    updateComponentAttrs({
      updates: [
        { attribute: "internalPaddingTop", value },
        { attribute: "internalPaddingBottom", value },
        { attribute: "internalPaddingLeft", value },
        { attribute: "internalPaddingRight", value },
        { attribute: "internalPaddingApplyToAll", value: checked },
      ],
      component: "button",
    });
  };

  const updateInternalPaddingTop = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "internalPaddingTop", value }],
      component: "button",
    });
  };

  const updateInternalPaddingBottom = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "internalPaddingBottom", value }],
      component: "button",
    });
  };

  const updateInternalPaddingLeft = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "internalPaddingLeft", value }],
      component: "button",
    });
  };

  const updateInternalPaddingRight = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "internalPaddingRight", value }],
      component: "button",
    });
  };

  const updateAllInternalPadding = (value: number) => {
    updateComponentAttrs({
      updates: [
        { attribute: "internalPaddingTop", value },
        { attribute: "internalPaddingBottom", value },
        { attribute: "internalPaddingLeft", value },
        { attribute: "internalPaddingRight", value },
      ],
      component: "button",
    });
  };

  const toggleExternalPaddingToAll = (checked: boolean) => {
    if (!checked) {
      updateComponentAttrs({
        updates: [{ attribute: "externalPaddingApplyToAll", value: checked }],
        component: "button",
      });
      return;
    }
    const value = attributes?.externalPaddingTop ?? buttonStyle.externalPaddingTop;
    updateComponentAttrs({
      updates: [
        { attribute: "externalPaddingTop", value },
        { attribute: "externalPaddingBottom", value },
        { attribute: "externalPaddingLeft", value },
        { attribute: "externalPaddingRight", value },
        { attribute: "externalPaddingApplyToAll", value: checked },
      ],
      component: "button",
    });
  };

  const updateExternalPaddingTop = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "externalPaddingTop", value }],
      component: "button",
    });
  };

  const updateExternalPaddingBottom = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "externalPaddingBottom", value }],
      component: "button",
    });
  };

  const updateExternalPaddingLeft = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "externalPaddingLeft", value }],
      component: "button",
    });
  };

  const updateExternalPaddingRight = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "externalPaddingRight", value }],
      component: "button",
    });
  };

  const updateAllExternalPadding = (value: number) => {
    updateComponentAttrs({
      updates: [
        { attribute: "externalPaddingTop", value },
        { attribute: "externalPaddingBottom", value },
        { attribute: "externalPaddingLeft", value },
        { attribute: "externalPaddingRight", value },
      ],
      component: "button",
    });
  };

  const updateFontSize = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "fontSize", value }],
      component: "button",
    });
  };

  const updateFontFamily = (value: string) => {
    updateComponentAttrs({
      updates: [{ attribute: "fontFamily", value }],
      component: "button",
    });
  };

  const updateBorderRadius = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "borderRadius", value }],
      component: "button",
    });
  };

  const updateWidth = (value: "full" | "auto") => {
    updateComponentAttrs({
      updates: [{ attribute: "width", value }],
      component: "button",
    });
  };

  const changeLink = (value: string) => {
    updateComponentAttrs({
      updates: [{ attribute: "href", value }],
      component: "button",
    });
  };

  const handleLinkTypeChange = (value: "email" | "web" | "phone") => {
    const prefix = getLinkPrefix(value);
    if (href.startsWith(prefix)) return;
    updateComponentAttrs({
      updates: [{ attribute: "href", value: prefix }],
      component: "button",
    });
  };

  const onLinkBlur = (value: string) => {
    if (!isValidUrl(value)) {
      updateComponentAttrs({
        updates: [{ attribute: "href", value: "https://" }],
        component: "button",
      });
    }
  };

  const toggleNewTab = (checked: boolean) => {
    updateComponentAttrs({
      updates: [{ attribute: "target", value: checked ? "_blank" : "_self" }],
      component: "button",
    });
  };

  const updateAlignment = (value: "left" | "center" | "right") => {
    updateComponentAttrs({
      updates: [{ attribute: "align", value }],
      component: "button",
    });
  };

  return (
    <>
      <StylesSection>
        <StylesSectionHeader>Colors</StylesSectionHeader>
        <ColorPickerButton
          color={attributes?.backgroundColor ?? buttonStyle.backgroundColor}
          onClick={() =>
            setLocalColorPicker({
              attribute: "backgroundColor",
              component: "button",
            })
          }
          title="Background"
        />
        <ColorPickerButton
          color={attributes?.buttonColor ?? buttonStyle.buttonColor}
          onClick={() =>
            setLocalColorPicker({
              attribute: "buttonColor",
              component: "button",
            })
          }
          title="Button"
        />
        <ColorPickerButton
          title="Text"
          color={attributes?.color ?? buttonStyle.color}
          onClick={() =>
            setLocalColorPicker({
              attribute: "color",
              component: "button",
            })
          }
        />
      </StylesSection>
      <StylesSection>
        <StylesSectionHeader>Font</StylesSectionHeader>
        <CustomNumberInput
          key="fontSize"
          value={attributes?.fontSize ?? buttonStyle.fontSize}
          onChange={updateFontSize}
        />
        <Select value={attributes?.fontFamily ?? buttonStyle.fontFamily} onValueChange={updateFontFamily}>
          <SelectTrigger className="text-sm">
            <SelectValue placeholder="Font" />
          </SelectTrigger>
          <SelectContent className="z-[100] max-h-[280px]">
            <SelectGroup>
              {Object.entries(EMAIL_FONTS_MAP).map(([name, stack]) => (
                <SelectItem style={{ fontFamily: stack }} key={name} value={stack}>
                  {name}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      </StylesSection>
      <StylesSection>
        <StylesSectionHeader>Border Radius</StylesSectionHeader>
        <CustomNumberInput
          max={100}
          min={0}
          onChange={updateBorderRadius}
          value={attributes?.borderRadius ?? buttonStyle.borderRadius}
        />
      </StylesSection>
      <StylesSection>
        <StylesSectionHeader>Link</StylesSectionHeader>
        <Select value={linkType} onValueChange={handleLinkTypeChange}>
          <SelectTrigger className="h-14 w-full bg-light100">
            <SelectValue placeholder="Select link type" />
          </SelectTrigger>
          <SelectContent className="z-[99999]">
            <SelectGroup>
              <SelectItem value="web">Web</SelectItem>
              <SelectItem value="email">Email</SelectItem>
              <SelectItem value="phone">Phone</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
        <Input
          type="text"
          value={attributes?.href ?? ""}
          onChange={(e) => changeLink(e.target.value.trim())}
          onBlur={(e) => onLinkBlur(e.target.value.trim())}
          placeholder="https://example.com"
        />
        <div className="flex items-center gap-2">
          <Checkbox id="newTab" checked={attributes?.target === "_blank"} onCheckedChange={toggleNewTab} />
          <p className="text-sm">Open in new tab</p>
        </div>
      </StylesSection>
      <StylesSection>
        <StylesSectionHeader>Width</StylesSectionHeader>
        <div className="flex gap-3">
          <Button
            size="sm"
            variant={(attributes?.width ?? buttonStyle.width) === "auto" ? "default" : "outlineLight"}
            onClick={() => updateWidth("auto")}
            className="flex-1"
          >
            Auto
          </Button>
          <Button
            size="sm"
            variant={(attributes?.width ?? buttonStyle.width) === "full" ? "default" : "outlineLight"}
            onClick={() => updateWidth("full")}
            className="flex-1"
          >
            Full
          </Button>
        </div>
      </StylesSection>
      <StylesSection>
        <StylesSectionHeader>Alignment</StylesSectionHeader>
        <TabButtonsGroup
          buttons={
            <>
              <TabButton onClick={() => updateAlignment("left")} isActive={align === "left"} label="Left" />
              <TabButton
                onClick={() => updateAlignment("center")}
                isActive={align === "center"}
                label="Center"
              />
              <TabButton
                onClick={() => updateAlignment("right")}
                isActive={align === "right"}
                label="Right"
              />
            </>
          }
        />
      </StylesSection>
      <StylesSection>
        <div className="flex items-center justify-between">
          <StylesSectionHeader>Internal Padding</StylesSectionHeader>
          <div className="flex items-center gap-2">
            <Checkbox checked={internalPaddingApplyToAll} onCheckedChange={toggleInternalPaddingToAll} />
            <Label>Apply to all sides</Label>
          </div>
        </div>
        <div className={internalPaddingApplyToAll ? "" : "grid grid-cols-2 gap-2"}>
          {internalPaddingApplyToAll ? (
            <div className="space-y-1.5">
              <CustomNumberInput
                value={attributes?.internalPaddingTop ?? buttonStyle.internalPaddingTop}
                onChange={updateAllInternalPadding}
              />
            </div>
          ) : (
            <>
              <div className="space-y-1.5">
                <Label>Top</Label>
                <CustomNumberInput
                  max={100}
                  min={0}
                  onChange={updateInternalPaddingTop}
                  value={attributes?.internalPaddingTop ?? buttonStyle.internalPaddingTop}
                />
              </div>
              <div className="space-y-1.5">
                <Label>Bottom</Label>
                <CustomNumberInput
                  max={100}
                  min={0}
                  onChange={updateInternalPaddingBottom}
                  value={attributes?.internalPaddingBottom ?? buttonStyle.internalPaddingBottom}
                />
              </div>
              <div className="space-y-1.5">
                <Label>Left</Label>
                <CustomNumberInput
                  max={100}
                  min={0}
                  onChange={updateInternalPaddingLeft}
                  value={attributes?.internalPaddingLeft ?? buttonStyle.internalPaddingLeft}
                />
              </div>
              <div className="space-y-1.5">
                <Label>Right</Label>
                <CustomNumberInput
                  max={100}
                  min={0}
                  onChange={updateInternalPaddingRight}
                  value={attributes?.internalPaddingRight ?? buttonStyle.internalPaddingRight}
                />
              </div>
            </>
          )}
        </div>
      </StylesSection>
      <StylesSection>
        <div className="flex items-center justify-between">
          <StylesSectionHeader>External Padding</StylesSectionHeader>
          <div className="flex items-center gap-2">
            <Checkbox checked={externalPaddingApplyToAll} onCheckedChange={toggleExternalPaddingToAll} />
            <Label>Apply to all sides</Label>
          </div>
        </div>
        <div className={externalPaddingApplyToAll ? "" : "grid grid-cols-2 gap-2"}>
          {externalPaddingApplyToAll ? (
            <div className="space-y-1.5">
              <CustomNumberInput
                value={attributes?.externalPaddingTop ?? buttonStyle.externalPaddingTop}
                onChange={updateAllExternalPadding}
              />
            </div>
          ) : (
            <>
              <div className="space-y-1.5">
                <Label>Top</Label>
                <CustomNumberInput
                  max={100}
                  min={0}
                  onChange={updateExternalPaddingTop}
                  value={attributes?.externalPaddingTop ?? buttonStyle.externalPaddingTop}
                />
              </div>
              <div className="space-y-1.5">
                <Label>Bottom</Label>
                <CustomNumberInput
                  max={100}
                  min={0}
                  onChange={updateExternalPaddingBottom}
                  value={attributes?.externalPaddingBottom ?? buttonStyle.externalPaddingBottom}
                />
              </div>
              <div className="space-y-1.5">
                <Label>Left</Label>
                <CustomNumberInput
                  max={100}
                  min={0}
                  onChange={updateExternalPaddingLeft}
                  value={attributes?.externalPaddingLeft ?? buttonStyle.externalPaddingLeft}
                />
              </div>
              <div className="space-y-1.5">
                <Label>Right</Label>
                <CustomNumberInput
                  max={100}
                  min={0}
                  onChange={updateExternalPaddingRight}
                  value={attributes?.externalPaddingRight ?? buttonStyle.externalPaddingRight}
                />
              </div>
            </>
          )}
        </div>
      </StylesSection>
    </>
  );
};

export default ButtonStyles;
