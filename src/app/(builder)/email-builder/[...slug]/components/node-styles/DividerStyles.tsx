import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@components/shadcn/Select";
import { Checkbox } from "@components/shadcn/Checkbox";
import { DIVIDER_LINE_STYLES } from "@lib/builder/constants";
import { CustomNumberInput } from "@components/primitive/CustomNumberInput";
import StylesSectionHeader from "@components/builder/StylesSectionHeader";
import ColorPickerButton from "@components/builder/ColorPickerButton";
import Label from "@components/builder/Label";
import StylesSection from "@components/builder/StylesSection";
import useBuilderStore from "@stores/builder/useBuilderStore";
import { useEmailBuilderContext } from "@components/builder/context";
import capitalizeFirst from "@lib/capitalizeFirst";
import useComponentAttributes from "@hooks/builder/useComponentAttributes";
import Slider from "@components/primitive/Slider";
import useGlobalStylesStore from "@stores/builder/useGlobalStylesStore";

const DividerStyles = () => {
  const { dividerStyle } = useGlobalStylesStore();
  const { editorRef } = useEmailBuilderContext();
  const editor = editorRef.current;
  const { setLocalColorPicker } = useBuilderStore();
  const { updateComponentAttrs } = useComponentAttributes();
  const attributes = editor?.getAttributes("divider");
  const paddingApplyToAll = attributes?.paddingApplyToAll ?? dividerStyle.paddingApplyToAll;

  const updateLineThickness = (values: number[]) => {
    const value = values[0];
    updateComponentAttrs({
      updates: [{ attribute: "height", value }],
      component: "divider",
    });
  };

  const updateWidth = (values: number[]) => {
    const value = values[0];
    updateComponentAttrs({
      updates: [{ attribute: "width", value }],
      component: "divider",
    });
  };

  const updateLineStyle = (value: string) => {
    updateComponentAttrs({
      updates: [{ attribute: "style", value }],
      component: "divider",
    });
  };

  const handleApplyPaddingToAll = (checked: boolean) => {
    if (!checked) {
      updateComponentAttrs({
        updates: [{ attribute: "paddingApplyToAll", value: checked }],
        component: "divider",
      });
      return;
    }

    const value = attributes?.paddingTop ?? dividerStyle.paddingTop;
    updateComponentAttrs({
      updates: [
        { attribute: "paddingTop", value },
        { attribute: "paddingBottom", value },
        { attribute: "paddingLeft", value },
        { attribute: "paddingRight", value },
        { attribute: "paddingApplyToAll", value: checked },
      ],
      component: "divider",
    });
  };

  const updatePaddingTop = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "paddingTop", value }],
      component: "divider",
    });
  };

  const updatePaddingBottom = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "paddingBottom", value }],
      component: "divider",
    });
  };

  const updatePaddingLeft = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "paddingLeft", value }],
      component: "divider",
    });
  };

  const updatePaddingRight = (value: number) => {
    updateComponentAttrs({
      updates: [{ attribute: "paddingRight", value }],
      component: "divider",
    });
  };

  const updateAllPadding = (value: number) => {
    updateComponentAttrs({
      updates: [
        { attribute: "paddingTop", value },
        { attribute: "paddingBottom", value },
        { attribute: "paddingLeft", value },
        { attribute: "paddingRight", value },
      ],
      component: "divider",
    });
  };

  return (
    <>
      <StylesSection>
        <div className="flex items-center justify-between">
          <StylesSectionHeader>Thickness</StylesSectionHeader>
          <Label>{attributes?.height ?? dividerStyle.height}px</Label>
        </div>
        <Slider
          value={[attributes?.height ?? dividerStyle.height]}
          defaultValue={[1]}
          onValueChange={updateLineThickness}
          min={1}
          max={100}
          step={1}
        />
      </StylesSection>
      <StylesSection>
        <div className="flex items-center justify-between">
          <StylesSectionHeader>Width</StylesSectionHeader>
          <Label>{attributes?.width ?? dividerStyle.width}%</Label>
        </div>
        <Slider
          value={[attributes?.width ?? dividerStyle.width]}
          defaultValue={[1]}
          onValueChange={updateWidth}
          min={5}
          max={100}
          step={1}
        />
      </StylesSection>
      <StylesSection>
        <StylesSectionHeader>Style</StylesSectionHeader>
        <Select value={attributes?.style ?? dividerStyle.style} onValueChange={updateLineStyle}>
          <SelectTrigger>
            <SelectValue placeholder="Select line style" />
          </SelectTrigger>
          <SelectContent className="z-[999]">
            {DIVIDER_LINE_STYLES.map((style) => (
              <SelectItem key={style} value={style}>
                <div className="flex w-full items-center justify-center gap-0">
                  <span className="w-16 text-left text-sm">{capitalizeFirst(style)}</span>
                  <hr
                    className="h-0 border-t-2 border-black"
                    style={{
                      borderTopStyle: style as any,
                      width: "160px",
                    }}
                  />
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </StylesSection>
      <StylesSection>
        <div className="flex items-center justify-between">
          <StylesSectionHeader>Padding</StylesSectionHeader>
          <div className="flex items-center gap-2">
            <Checkbox checked={paddingApplyToAll} onCheckedChange={handleApplyPaddingToAll} />
            <Label>Apply to all</Label>
          </div>
        </div>
        <div className={paddingApplyToAll ? "" : "grid grid-cols-2 gap-3"}>
          {paddingApplyToAll ? (
            <CustomNumberInput
              value={attributes?.paddingTop ?? dividerStyle.paddingTop}
              onChange={updateAllPadding}
              className="h-8 w-full py-5"
            />
          ) : (
            <>
              <StylesSection>
                <Label>Top</Label>
                <CustomNumberInput
                  value={attributes?.paddingTop ?? dividerStyle.paddingTop}
                  onChange={updatePaddingTop}
                  min={0}
                  max={100}
                />
              </StylesSection>
              <StylesSection>
                <Label>Bottom</Label>
                <CustomNumberInput
                  value={attributes?.paddingBottom ?? dividerStyle.paddingBottom}
                  onChange={updatePaddingBottom}
                />
              </StylesSection>
              <StylesSection>
                <Label>Left</Label>
                <CustomNumberInput
                  value={attributes?.paddingLeft ?? dividerStyle.paddingLeft}
                  onChange={updatePaddingLeft}
                  className="h-8 py-5"
                />
              </StylesSection>
              <StylesSection>
                <Label>Right</Label>
                <CustomNumberInput
                  value={attributes?.paddingRight ?? dividerStyle.paddingRight}
                  onChange={updatePaddingRight}
                  className="h-8 py-5"
                />
              </StylesSection>
            </>
          )}
        </div>
      </StylesSection>
      <StylesSection>
        <StylesSectionHeader>Colors</StylesSectionHeader>
        <ColorPickerButton
          title="Line Color"
          color={attributes?.color ?? dividerStyle.color}
          onClick={() =>
            setLocalColorPicker({
              attribute: "color",
              component: "divider",
            })
          }
        />
        <ColorPickerButton
          title="Block Background"
          color={attributes?.backgroundColor ?? dividerStyle.backgroundColor}
          onClick={() =>
            setLocalColorPicker({
              attribute: "backgroundColor",
              component: "divider",
            })
          }
        />
      </StylesSection>
    </>
  );
};

export default DividerStyles;
