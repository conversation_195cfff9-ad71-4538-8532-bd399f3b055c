import { useState } from "react";
import Link from "next/link";
import * as VisuallyHidden from "@radix-ui/react-visually-hidden";
import { Dialog, DialogContent, DialogTitle, DialogDescription } from "@components/shadcn/Dialog";
import { Input } from "@components/shadcn/Input";
import Button from "@components/primitive/Button";
import { toast } from "sonner";
import { sendTestEmail } from "@server/event/sendTestEmail";
import useError from "@hooks/useError";
import { usePathParams } from "@hooks/builder/usePathParams";
import useAccount from "@hooks/useAccount";
import useProfile from "@hooks/useProfile";
import useBuilderStore from "@stores/builder/useBuilderStore";

interface Props {
  userId: string;
}

export default function SendTestEmailDialog({ userId }: Props) {
  const { isTestEmailOpen, setIsTestEmailOpen } = useBuilderStore();
  const { account } = useAccount({ userId });
  const { profile } = useProfile({ userId });
  const { emailCampaignId, eventId } = usePathParams();
  const [loading, setLoading] = useState(false);
  const { handleError } = useError();
  const email = account?.email;
  const name = profile?.name;

  const handleSendTest = async () => {
    try {
      setLoading(true);
      const result = await sendTestEmail({ eventId, emailCampaignId });
      if ("error" in result) throw new Error(result.error);
      toast("Test email sent successfully");
      setIsTestEmailOpen(false);
    } catch (e) {
      console.error(e);
      handleError(e);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isTestEmailOpen} onOpenChange={setIsTestEmailOpen}>
      <DialogContent className="gap-4 p-0 lg:gap-4">
        <div className="border-b border-light200 px-10 py-4">
          <DialogTitle className="text-center">Email Test Centre</DialogTitle>
        </div>
        <VisuallyHidden.Root>
          <DialogDescription className="text-center">
            Preview this email in your inbox. Email will be sent to the email address of your account.
          </DialogDescription>
        </VisuallyHidden.Root>
        <div className="flex flex-col gap-2 px-10 py-2">
          <div className="px-2">
            <h3 className="text-base font-medium">Sending a test email to</h3>
          </div>
          <Input type="email" disabled={true} placeholder={`${name}:${email}`} value={`${name}: ${email}`} />
          <div className="px-2">
            <p className="text-sm text-gray-500">
              Preview this email in your inbox. Email will be sent to the email address of your account.
            </p>
          </div>
        </div>
        <div className="flex justify-between border-t border-light200 bg-white px-10 py-4">
          <div className="flex items-center gap-2">
            <Link
              href="/terms"
              target="_blank"
              className="text-sm text-gray-500 hover:text-gray-900 hover:underline"
            >
              Terms
            </Link>
            <p> | </p>
            <Link
              href="/privacy"
              target="_blank"
              className="text-sm text-gray-500 hover:text-gray-900 hover:underline"
            >
              Privacy
            </Link>
          </div>
          <div className="flex gap-2">
            <Button text="Cancel" variant="outlineLight" onClick={() => setIsTestEmailOpen(false)} />
            <Button
              text={loading ? "Sending..." : "Send test"}
              loading={loading}
              onClick={handleSendTest}
              disabled={loading}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
