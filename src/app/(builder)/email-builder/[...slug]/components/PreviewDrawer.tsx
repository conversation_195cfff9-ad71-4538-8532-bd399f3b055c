import {
  Drawer,
  Drawer<PERSON>ontent,
  DrawerHeader,
  DrawerTitle,
  DrawerDescription,
} from "src/components/shadcn/Drawer";
import * as VisuallyHidden from "@radix-ui/react-visually-hidden";
import useBuilderStore from "@stores/builder/useBuilderStore";
import useEmailHtml from "@hooks/builder/useEmailHtml";
import { useEffect, useRef } from "react";

const PreviewDrawer = () => {
  const { getEmailHtml } = useEmailHtml();
  const { isPreviewOpen, setIsPreviewOpen } = useBuilderStore();
  const emailHtml = getEmailHtml();

  const desktopIframeRef = useRef<HTMLIFrameElement>(null);
  const mobileIframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    if (!isPreviewOpen || !emailHtml) return;

    const injectHtml = (iframe: HTMLIFrameElement | null) => {
      if (!iframe) return;
      iframe.srcdoc = emailHtml;
    };

    const timer = setTimeout(() => {
      injectHtml(desktopIframeRef.current);
      injectHtml(mobileIframeRef.current);
    }, 100);

    return () => clearTimeout(timer);
  }, [emailHtml, isPreviewOpen]);

  return (
    <Drawer open={isPreviewOpen} onOpenChange={(open) => setIsPreviewOpen(open)}>
      <DrawerContent className="z-[999] mx-8 h-[96vh] rounded-lg bg-white px-0 md:px-0">
        <DrawerHeader className="flex items-center justify-between rounded-lg border-b border-light200 bg-white py-6">
          <DrawerTitle className="px-3">Preview</DrawerTitle>
          <VisuallyHidden.Root>
            <DrawerDescription />
          </VisuallyHidden.Root>
          <div className="px-3">
            <button onClick={() => setIsPreviewOpen(false)}>Close</button>
          </div>
        </DrawerHeader>
        <div className="flex flex-1 items-start justify-center gap-4 p-4">
          <div className="h-full flex-1 flex-col rounded-2xl border border-gray-200 bg-white">
            <div className="flex h-[40px] w-full items-center rounded-t-2xl border-b border-gray-200 bg-gray-100">
              <div className="flex w-32 items-center justify-center gap-3">
                <span className="h-3 w-3 rounded-full bg-gray-200" />
                <span className="h-3 w-3 rounded-full bg-gray-200" />
                <span className="h-3 w-3 rounded-full bg-gray-200" />
              </div>
            </div>
            <div className={"h-[calc(100%-40px)] w-full overflow-y-auto"}>
              <iframe
                ref={desktopIframeRef}
                title="Desktop Email Preview"
                className="h-full w-full border-0"
                sandbox="allow-same-origin"
              />
            </div>
          </div>
          <div className="relative h-full w-[375px] rounded-[40px] border-[10px] border-gray-100 shadow-sm">
            <div className="absolute left-1/2 top-0 h-[10px] w-[120px] -translate-x-1/2 transform rounded-b-[20px] bg-gray-100" />
            <div className={`h-full w-full overflow-y-auto pt-[15px]`}>
              <iframe
                ref={mobileIframeRef}
                title="Mobile Email Preview"
                className="h-full w-full border-0"
                sandbox="allow-same-origin"
                style={{ width: "100%" }}
              />
            </div>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default PreviewDrawer;
