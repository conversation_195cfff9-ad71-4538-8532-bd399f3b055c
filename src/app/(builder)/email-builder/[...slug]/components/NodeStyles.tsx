import { useMemo } from "react";
import ButtonStyles from "./node-styles/ButtonStyles";
import ImageStyles from "./node-styles/ImageStyles";
import SpacerStyles from "./node-styles/SpacerStyles";
import DividerStyles from "./node-styles/DividerStyles";
import LayoutStyles from "./node-styles/LayoutStyles";
import useBuilderStore from "@stores/builder/useBuilderStore";
import { SidePanelHeader } from "@components/builder/PanelHeader";
import capitalizeFirst from "@lib/capitalizeFirst";
import TextStyles from "./node-styles/TextStyles";
import SectionStyles from "./node-styles/SectionStyles";
import { useEmailBuilderContext } from "@components/builder/context";
import { ALL_COMPONENT_ITEMS } from "@lib/builder/constants";
import { getClosestViewableParentType, traverseNode } from "@lib/builder/node";

export default function NodeStyles() {
  const { editorRef } = useEmailBuilderContext();
  const editor = editorRef.current;
  const { focusedItem } = useBuilderStore();
  const type = focusedItem?.type.name || "";
  const isVisible = !!focusedItem && ALL_COMPONENT_ITEMS.includes(type);
  const targetParentType = useMemo(() => {
    return type ? getClosestViewableParentType(type) : "";
  }, [type]);

  const renderTitle = useMemo(() => {
    if (!type) return "Styles";
    if (type === "textBlock") return "Block";
    else return `${capitalizeFirst(type)}`;
  }, [type]);

  const renderStyleComponent = () => {
    switch (type) {
      case "textBlock":
        return <TextStyles />;
      case "button":
        return <ButtonStyles />;
      case "image":
        return <ImageStyles />;
      case "layout":
        return <LayoutStyles />;
      case "section":
        return <SectionStyles />;
      case "spacer":
        return <SpacerStyles />;
      case "divider":
        return <DividerStyles />;
      default:
        return null;
    }
  };

  const closeDrawer = () => {
    if (!editor) return;
    editor.commands.setNodeSelection(-1);
  };

  const onBackClick = () => {
    if (!editor) return;

    const resolvedPos = editor.view.state.selection.$from;
    const parentNode = traverseNode(resolvedPos, targetParentType);
    if (!parentNode) return;

    editor
      .chain()
      .blur()
      .setNodeSelection(parentNode?.pos + 1)
      .run();
  };

  if (!isVisible) return null;
  return (
    <div className="flex w-[400px] flex-shrink-0 flex-col border-gray-200 bg-white">
      <SidePanelHeader
        title={renderTitle}
        onClose={closeDrawer}
        backTitle={targetParentType}
        onBackClick={onBackClick}
      />
      <div className="flex flex-col gap-8 overflow-y-auto px-5 py-4">{renderStyleComponent()}</div>
    </div>
  );
}
