import React from "react";

export default function LoopyahFooter() {
  return (
    <div className="bg-[#f4f4f4] p-4">
      <p
        style={{
          textAlign: "center",
          fontFamily: "Helvetica",
          fontSize: "14px",
          lineHeight: "1.5",
        }}
      >
        This email was sent to you by the event creator as you&apos;re a part of this event.
      </p>
      <p
        style={{
          textAlign: "center",
          fontFamily: "Helvetica",
        }}
      >
        <a
          href="https://loopyah.com"
          target="_blank"
          rel="noopener noreferrer"
          style={{
            color: "#000000",
            fontWeight: "normal",
            fontSize: "14px",
            lineHeight: "1.5",
          }}
        >
          loopyah.com
        </a>{" "}
        |{" "}
        <a
          href="mailto:<EMAIL>"
          target="_blank"
          rel="noopener noreferrer"
          style={{
            color: "#000000",
            fontWeight: "normal",
            fontSize: "14px",
            lineHeight: "1.5",
          }}
        >
          <EMAIL>
        </a>
      </p>
      <p
        style={{
          textAlign: "center",
          fontFamily: "Helvetica",
        }}
      >
        <a
          rel="noopener noreferrer"
          style={{
            color: "#000000",
            fontWeight: "normal",
            fontSize: "14px",
            lineHeight: "1.5",
            textDecoration: "underline",
          }}
        >
          Manage Preferences
        </a>
        {" | "}
        <a
          rel="noopener noreferrer"
          style={{
            color: "#000000",
            fontWeight: "normal",
            fontSize: "14px",
            lineHeight: "1.5",
            textDecoration: "underline",
          }}
        >
          Unsubscribe
        </a>
      </p>
    </div>
  );
}
