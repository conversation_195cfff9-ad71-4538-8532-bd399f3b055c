import { useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/shadcn/Select";
import { Input } from "@components/shadcn/Input";
import { useEmailBuilderContext } from "@components/builder/context";
import { getLinkPrefix, getLinkPlaceholder } from "@lib/builder/link";
import { getLinkType, isValidLink } from "@lib/builder/link";
import CloseIcon from "@components/builder/CloseIcon";
import StylesSectionHeader from "@components/builder/StylesSectionHeader";
import { Checkbox } from "@components/shadcn/Checkbox";
import Label from "@components/builder/Label";
import Button from "@components/primitive/Button";

interface Props {
  onClose: () => void;
}

const LinkPopover = ({ onClose }: Props) => {
  const { editorRef } = useEmailBuilderContext();
  const editor = editorRef.current;
  const hrefValue = editor?.getAttributes("link").href || "";
  const targetValue = editor?.getAttributes("link").target || "_self";
  const [href, setHref] = useState<string>(hrefValue);
  const [target, setTarget] = useState(targetValue);
  const openInNewTab = target === "_blank";
  const linkType = getLinkType(href);

  useEffect(() => {
    setHref(hrefValue);
  }, [hrefValue]);

  const handleLinkTypeChange = (value: "email" | "web" | "phone") => {
    const prefix = getLinkPrefix(value);
    if (href.startsWith(prefix)) return;
    setHref(prefix);
  };

  const onInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/ /g, "");
    setHref(value);
  };

  const onOpenInNewTabChange = (checked: boolean) => {
    const newTarget = checked ? "_blank" : "_self";
    setTarget(newTarget);
  };

  const handleClose = () => {
    const linkValue = href
      .replace("https://", "")
      .replace("http://", "")
      .replace("www.", "")
      .replace("tel:", "")
      .replace("mailto:", "");

    if (!linkValue) {
      editor?.commands.unsetLink();
    }

    onClose();
  };

  const handleSave = () => {
    if (!editor) return;

    if (!href) {
      editor.commands.unsetLink();
    } else {
      editor.commands.setLink({ target, href });
    }

    onClose();
  };

  return (
    <div className="flex w-[400px] flex-col gap-4">
      <div className="flex items-center justify-between border-b border-light200 px-4 pb-4 pt-4">
        <StylesSectionHeader>Text Link</StylesSectionHeader>
        <CloseIcon onClick={handleClose} />
      </div>
      <div className="flex flex-col gap-4 px-4 pb-6">
        <Select value={linkType} onValueChange={handleLinkTypeChange}>
          <SelectTrigger className="h-14 w-full bg-light100">
            <SelectValue placeholder="Select link type" />
          </SelectTrigger>
          <SelectContent className="z-[99999]">
            <SelectGroup>
              <SelectItem value="web">Web</SelectItem>
              <SelectItem value="email">Email</SelectItem>
              <SelectItem value="phone">Phone</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
        <Input
          type="text"
          value={href}
          autoFocus={true}
          onChange={onInputChange}
          placeholder={getLinkPlaceholder(linkType)}
          color={"light"}
          className="h-14"
        />
        <div className="flex items-center gap-2">
          <Checkbox checked={openInNewTab} onCheckedChange={onOpenInNewTabChange} />
          <Label>Open link in new tab</Label>
        </div>
        <Button fullWidth onClick={handleSave} disabled={!isValidLink(href)} text="Save" />
      </div>
    </div>
  );
};

export default LinkPopover;
