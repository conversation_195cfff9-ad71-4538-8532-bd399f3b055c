import { cn } from "@lib/utils";
import HeadingControls from "./HeadingControls";
import FontControls from "./FontControls";
import ColorControls from "./ColorControls";
import FormattingControls from "./FormattingControls";
import LinkControls from "./LinkControls";
import AlignmentControls from "./AlignmentControls";
import ListControls from "./ListControls";
import MergeTagsDropdown from "./MergeTagsDropdown";
import { useEmailBuilderContext } from "@components/builder/context";
import useBuilderStore from "@stores/builder/useBuilderStore";

const Toolbar = () => {
  const { editorRef } = useEmailBuilderContext();
  const { isLoading } = useBuilderStore();
  const editor = editorRef.current;
  const type = editor?.state.selection.$from.parent.type.name || "";
  const isVisible = ["paragraph", "heading"].includes(type);

  if (isLoading) return null;
  return (
    <div
      className={cn(
        "sticky top-0 z-[50] flex h-[60px] min-h-[60px] items-center gap-2 overflow-x-scroll border-b border-l border-r border-light300 bg-white shadow-sm",
      )}
    >
      <div
        className={cn(
          "flex select-none items-center gap-2 p-2",
          !isVisible && "pointer-events-none relative cursor-not-allowed opacity-50",
        )}
      >
        <HeadingControls />
        <FontControls />
        <ColorControls />
        <FormattingControls />
        <LinkControls />
        <AlignmentControls />
        <ListControls />
        <MergeTagsDropdown />
      </div>
    </div>
  );
};

export default Toolbar;
