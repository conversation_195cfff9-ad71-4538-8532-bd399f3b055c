import React from "react";
import { List, ListOrdered } from "lucide-react";
import Tooltip from "@components/primitive/Tooltip";
import { useEmailBuilderContext } from "@components/builder/context";

const ListControls = () => {
  const { editorRef } = useEmailBuilderContext();
  const editor = editorRef.current;
  const canWrapInList = editor?.can().wrapInList("bulletList");

  const handleBulletList = () => {
    if (!editor) return;

    if (!canWrapInList) {
      const ListItemAttributes = editor.getAttributes("listItem");
      const paragraphAttributes = editor.getAttributes("paragraph");
      editor
        .chain()
        .clearNodes()
        .liftListItem("listItem")
        .updateAttributes("paragraph", {
          ...ListItemAttributes,
          ...paragraphAttributes,
        })
        .run();
    } else {
      editor.commands.wrapInList("bulletList");
    }
  };

  const handleOrderedList = () => {
    if (!editor) return;
    if (!canWrapInList) {
      const ListItemAttributes = editor.getAttributes("listItem");
      const paragraphAttributes = editor.getAttributes("paragraph");
      editor
        .chain()
        .clearNodes()
        .liftListItem("listItem")
        .updateAttributes("paragraph", {
          ...ListItemAttributes,
          ...paragraphAttributes,
        })
        .run();
    } else {
      editor.commands.wrapInList("orderedList");
    }
  };

  return (
    <div className="flex items-center gap-1">
      <Tooltip label="Bullet List">
        <button
          onClick={handleBulletList}
          //disabled={!editor?.can().toggleBulletList()}
          className={`rounded p-2 transition-colors duration-200 hover:bg-gray-100 disabled:opacity-25 ${
            editor?.isActive("bulletList") && "bg-light200"
          }`}
        >
          <List size={18} />
        </button>
      </Tooltip>

      <Tooltip label="Numbered List">
        <button
          disabled={!editor?.can().toggleOrderedList()}
          onClick={handleOrderedList}
          className={`rounded p-2 transition-colors duration-200 hover:bg-gray-100 disabled:opacity-25 ${editor?.isActive("orderedList") && "bg-light200"}`}
        >
          <ListOrdered size={18} />
        </button>
      </Tooltip>
    </div>
  );
};

export default ListControls;
