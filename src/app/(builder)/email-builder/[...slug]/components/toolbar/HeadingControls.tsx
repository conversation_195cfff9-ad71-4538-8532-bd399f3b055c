import React from "react";
import { Level } from "@tiptap/extension-heading";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/shadcn/Select";
import { useEmailBuilderContext } from "@components/builder/context";

const HEADING_OPTIONS = [
  { label: "Heading 1", level: 1 as Level },
  { label: "Heading 2", level: 2 as Level },
  { label: "Heading 3", level: 3 as Level },
  { label: "Paragraph", level: 0 as Level },
];

const HeadingControls = () => {
  const { editorRef } = useEmailBuilderContext();
  const editor = editorRef.current;
  const type = editor?.state.selection.$from.parent.type.name || "";
  const level = editor?.getAttributes(type).level ?? 0;
  const value = HEADING_OPTIONS.find((o) => o.level === level)?.label || "";

  const onClick = (label: string) => {
    const selected = HEADING_OPTIONS.find((o) => o.label === label);
    if (!selected) return;

    const level = selected.level;

    if (Number(level) === 0) {
      editor?.chain().focus().setParagraph().run();
    } else {
      editor?.chain().focus().toggleHeading({ level }).run();
    }
  };

  return (
    <div className="flex items-center">
      <Select value={value} onValueChange={onClick}>
        <SelectTrigger className="h-8 w-[120px] text-sm">
          <SelectValue placeholder="Select heading" />
        </SelectTrigger>
        <SelectContent className="z-[100]">
          <SelectGroup>
            {HEADING_OPTIONS.map((option) => (
              <SelectItem key={option.label} value={option.label}>
                {option.label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
};

export default HeadingControls;
