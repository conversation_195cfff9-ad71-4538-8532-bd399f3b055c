import React from "react";
import { cn } from "@lib/utils";
import { Type } from "lucide-react";
import Tooltip from "@components/primitive/Tooltip";
import FormatColorHighlightIcon from "mdi-react/FormatColorHighlightIcon";
import { useEmailBuilderContext } from "@components/builder/context";
import useBuilderStore, { LocalColorPicker } from "@stores/builder/useBuilderStore";
import useGlobalStylesStore from "@stores/builder/useGlobalStylesStore";
import {
  getSelectionNodeType,
  getSelectionStyleKey,
  getSelectionType,
  getSelectionValue,
} from "@lib/builder/selection";

const ColorControls = () => {
  const { setLocalColorPicker } = useBuilderStore();
  const { textStyle } = useGlobalStylesStore();
  const { editorRef } = useEmailBuilderContext();
  const editor = editorRef.current;

  const type = getSelectionNodeType(editor);
  const styleKey = getSelectionStyleKey(editor);
  const selectionType = getSelectionType(editor);

  const localFontColor = getSelectionValue({ editor, attribute: "color" });
  const globalFontColor = textStyle[styleKey].color;
  const fontColor = localFontColor || globalFontColor;
  const showColorEmpty = !fontColor || fontColor === "transparent";

  const backgroundColor = editor?.getAttributes("highlight")?.color || "";
  const showBackroundEmpty = !backgroundColor || backgroundColor === "transparent";

  return (
    <div className="flex items-center gap-1 px-2">
      <div className="relative">
        <Tooltip label="Text color">
          <button
            className="relative rounded p-2 hover:bg-gray-100"
            onClick={() =>
              setLocalColorPicker({
                attribute: "color",
                component: type as LocalColorPicker["component"],
              })
            }
          >
            <Type size={20} />
            <div
              style={{ backgroundColor: fontColor }}
              className={cn(
                "absolute bottom-1 right-1 h-[10px] w-[10px] rounded-full",
                showColorEmpty && "border border-gray-200",
              )}
            />
          </button>
        </Tooltip>
      </div>
      <div className="relative">
        <Tooltip label="Text highlight">
          <div className="relative">
            <button
              className="relative rounded p-2 hover:bg-gray-100"
              onClick={() =>
                selectionType !== "none" &&
                setLocalColorPicker({
                  attribute: "textHighlight",
                  component: type as LocalColorPicker["component"],
                })
              }
            >
              <FormatColorHighlightIcon size={22} />
              <div
                style={{ backgroundColor }}
                className={cn(
                  "absolute bottom-1 right-1 h-[10px] w-[10px] rounded-full",
                  showBackroundEmpty && "border border-gray-200",
                )}
              />
            </button>
          </div>
        </Tooltip>
      </div>
    </div>
  );
};

export default ColorControls;
