import { useState } from "react";
import { Link } from "lucide-react";
import Tooltip from "@components/primitive/Tooltip";
import { useEmailBuilderContext } from "@components/builder/context";
import { Popover } from "@components/shadcn/Popover";
import { PopoverContent, PopoverTrigger } from "@components/shadcn/Popover";
import LinkPopover from "./LinkPopover";

const LinkControls = () => {
  const [open, setOpen] = useState(false);
  const { editorRef } = useEmailBuilderContext();
  const editor = editorRef.current;
  const href = editor?.getAttributes("link").href || "";

  const openLinkDialog = () => {
    if (!editor) return;
    const selection = editor.state.selection;
    const { from, to } = selection;
    if (from === to) return;
    if (!!href) return setOpen(true);
    editor?.commands.setLink({ target: "_blank", href: "https://" });
    setOpen(true);
  };

  return (
    <Popover open={open}>
      <PopoverTrigger asChild>
        <div className="flex items-center gap-1">
          <Tooltip label="Add Link">
            <button
              onClick={openLinkDialog}
              className={`rounded p-2 hover:bg-gray-100 ${editor?.isActive("link") && "bg-light200"}`}
            >
              <Link size={18} />
            </button>
          </Tooltip>
        </div>
      </PopoverTrigger>
      <PopoverContent
        alignOffset={0}
        side="bottom"
        sideOffset={20}
        className="w-auto p-0 shadow-sm"
        align="start"
      >
        <LinkPopover onClose={() => setOpen(false)} />
      </PopoverContent>
    </Popover>
  );
};

export default LinkControls;
