import React, { useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/shadcn/Select";
import Tooltip from "@components/primitive/Tooltip";
import { ChevronUp, ChevronDown } from "lucide-react";
import { useEmailBuilderContext } from "@components/builder/context";
import { EMAIL_FONTS_MAP } from "@lib/builder/font";
import {
  getSelectionNodeType,
  getSelectionStyleKey,
  getSelectionType,
  getSelectionValue,
} from "@lib/builder/selection";
import useGlobalStylesStore from "@stores/builder/useGlobalStylesStore";

const FontControls = () => {
  const { textStyle } = useGlobalStylesStore();
  const { editorRef } = useEmailBuilderContext();
  const editor = editorRef.current;

  const type = getSelectionNodeType(editor);
  const styleKey = getSelectionStyle<PERSON>ey(editor);
  const selectionType = getSelectionType(editor);

  const localFontSize = getSelectionValue({ editor, attribute: "fontSize" });
  const globalFontSize = textStyle[styleKey].fontSize;
  const fontSize = localFontSize || globalFontSize;
  const [fontSizeInput, setFontSizeInput] = useState<string>("");

  const localFontFamily = getSelectionValue({ editor, attribute: "fontFamily" });
  const globalFontFamily = textStyle[styleKey]?.fontFamily;
  const fontFamily = localFontFamily || globalFontFamily;

  useEffect(() => {
    setFontSizeInput(fontSize.toString());
  }, [fontSize]);

  const changeFontFamily = (fontFamily: string) => {
    if (!editor) return;
    if (selectionType === "none") return;

    const chain = editor.chain().focus();

    if (selectionType === "partial") {
      chain.setFontFamily(fontFamily);
    } else {
      chain.updateAttributes(type, { fontFamily }).unsetFontFamily();
    }

    chain.run();
  };

  const changeFontSize = (fontSize: number) => {
    if (!editor) return;
    if (selectionType === "none") return;

    if (fontSize < 8) fontSize = 8;

    const chain = editor.chain().focus();
    if (selectionType === "partial") {
      chain.setFontSize(fontSize);
    } else {
      chain.updateAttributes(type, { fontSize }).unsetFontSize();
    }

    chain.run();
  };

  const onFontSizeInputBlur = (input: string) => {
    const value = Number(input.replace(/\D/g, ""));
    if (isNaN(value) || value < 8) return changeFontSize(textStyle[styleKey].fontSize);
    changeFontSize(value);
  };

  return (
    <div className="flex items-center gap-2">
      <Select value={fontFamily} onValueChange={changeFontFamily}>
        <SelectTrigger className="h-8 w-[160px] text-sm">
          <SelectValue placeholder="Font" />
        </SelectTrigger>
        <SelectContent className="z-[100] max-h-[300px]">
          <SelectGroup>
            {Object.entries(EMAIL_FONTS_MAP).map(([name, stack]) => (
              <SelectItem style={{ fontFamily: stack }} key={name} value={stack}>
                {name}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
      <div className="flex items-center">
        <Tooltip label="Font Size">
          <div className="flex h-8 overflow-hidden rounded border">
            <input
              value={fontSizeInput}
              onChange={(e) => setFontSizeInput(e.target.value)}
              className="w-10 text-center text-sm"
              onBlur={(e) => onFontSizeInputBlur(e.target.value)}
            />
            <div className="flex flex-col justify-between border-l">
              <button
                className="flex-1 px-1 outline-light200 hover:bg-light200"
                onClick={() => changeFontSize(fontSize + 1)}
              >
                <ChevronUp className="h-3 w-3" />
              </button>
              <button
                className="flex-1 px-1 outline-light200 hover:bg-light200"
                onClick={() => changeFontSize(fontSize - 1)}
              >
                <ChevronDown className="h-3 w-3" />
              </button>
            </div>
          </div>
        </Tooltip>
      </div>
    </div>
  );
};

export default FontControls;
