import React, { useState } from "react";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger } from "@components/shadcn/Select";
import { useEmailBuilderContext } from "@components/builder/context";

const MergeTagsDropdown = () => {
  const { editorRef } = useEmailBuilderContext();
  const editor = editorRef.current;
  const [value, setValue] = useState<string>("");

  const tags = [
    { label: "First Name", value: "first_name" },
    { label: "Last Name", value: "last_name" },
    { label: "Email", value: "email" },
    { label: "Full Name", value: "full_name" },
  ];

  const insertTag = (tagValue: string) => {
    if (!editor) return;
    editor.chain().focus().insertContent(`%%${tagValue}%%`).run();
    setValue("");
  };

  return (
    <div className="flex items-center px-2">
      <Select
        value={value}
        onValueChange={(value) => {
          insertTag(value);
        }}
      >
        <SelectTrigger className="flex h-8 w-[135px] items-center gap-1.5 text-xs">
          <span className="text-xs">Merge Tags</span>
        </SelectTrigger>
        <SelectContent className="z-[100]">
          <SelectGroup>
            {tags.map((tag) => (
              <SelectItem key={tag.value} value={tag.value} className="text-xs">
                {tag.label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
};

export default MergeTagsDropdown;
