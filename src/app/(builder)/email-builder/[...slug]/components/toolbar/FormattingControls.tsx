import React from "react";
import { Bold, Italic, Underline as UnderlineIcon, Strikethrough } from "lucide-react";
import Tooltip from "@components/primitive/Tooltip";
import { useEmailBuilderContext } from "@components/builder/context";
import {
  getSelectionNodeType,
  getSelectionStyleKey,
  getSelectionType,
  getSelectionValue,
} from "@lib/builder/selection";
import useGlobalStylesStore from "@stores/builder/useGlobalStylesStore";

const FormattingControls = () => {
  const { textStyle } = useGlobalStylesStore();
  const { editorRef } = useEmailBuilderContext();
  const editor = editorRef.current;

  const type = getSelectionNodeType(editor);
  const styleKey = getSelectionStyleKey(editor);
  const selectionType = getSelectionType(editor);

  const localFontWeight = getSelectionValue({ editor, attribute: "fontWeight" });
  const globalFontWeight = textStyle[styleKey].fontWeight;
  const fontWeight = localFontWeight || globalFontWeight;

  const localFontStyle = getSelectionValue({ editor, attribute: "fontStyle" });
  const globalFontStyle = textStyle[styleKey].fontStyle;
  const fontStyle = localFontStyle || globalFontStyle;

  const localTextDecoration = getSelectionValue({ editor, attribute: "textDecoration" });
  const globalTextDecoration = textStyle[styleKey].textDecoration;
  const textDecoration = localTextDecoration || globalTextDecoration;

  const toggleStrikeThrough = () => {
    if (!editor) return;
    if (selectionType === "none") return;
    editor.chain().focus().toggleStrike().run();
  };

  const toggleFontWeight = () => {
    if (!editor) return;
    if (selectionType === "none") return;

    const value = fontWeight === "bold" ? "normal" : "bold";

    const chain = editor.chain().focus();

    if (selectionType === "partial") {
      chain.setFontWeight(value);
    } else {
      chain.updateAttributes(type, { fontWeight: value }).unsetFontWeight();
    }

    chain.run();
  };

  const toggleFontStyle = () => {
    if (!editor) return;
    if (selectionType === "none") return;

    const value = fontStyle === "italic" ? "normal" : "italic";

    const chain = editor.chain().focus();

    if (selectionType === "partial") {
      chain.setFontStyle(value);
    } else {
      chain.updateAttributes(type, { fontStyle: value }).unsetFontStyle();
    }

    chain.run();
  };

  const toggleTextDecoration = () => {
    if (!editor) return;
    if (selectionType === "none") return;

    const value = textDecoration === "underline" ? "none" : "underline";

    const chain = editor.chain().focus();

    if (selectionType === "partial") {
      chain.setTextDecoration(value);
    } else {
      chain.updateAttributes(type, { textDecoration: value }).unsetTextDecoration();
    }

    chain.run();
  };

  return (
    <div className="flex items-center gap-1 border-l border-r px-2">
      <Tooltip label="Bold">
        <button
          onClick={toggleFontWeight}
          className={`rounded p-2 hover:bg-gray-100 ${fontWeight === "bold" && "bg-light200"}`}
          disabled={selectionType === "none"}
        >
          <Bold size={18} />
        </button>
      </Tooltip>
      <Tooltip label="Italic">
        <button
          onClick={toggleFontStyle}
          className={`rounded p-2 hover:bg-gray-100 ${fontStyle === "italic" && "bg-light200"}`}
          disabled={selectionType === "none"}
        >
          <Italic size={18} />
        </button>
      </Tooltip>

      <Tooltip label="Underline">
        <button
          onClick={toggleTextDecoration}
          className={`rounded p-2 hover:bg-gray-100 ${textDecoration === "underline" && "bg-light200"}`}
          disabled={selectionType === "none"}
        >
          <UnderlineIcon size={18} />
        </button>
      </Tooltip>
      <Tooltip label="Strikethrough">
        <button
          onClick={toggleStrikeThrough}
          className={`rounded p-2 hover:bg-gray-100 ${editor?.isActive("strike") && "bg-light200"}`}
          disabled={selectionType === "none"}
        >
          <Strikethrough size={18} />
        </button>
      </Tooltip>
    </div>
  );
};

export default FormattingControls;
