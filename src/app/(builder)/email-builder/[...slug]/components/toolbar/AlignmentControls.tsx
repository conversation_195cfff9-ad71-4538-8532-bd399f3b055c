import React from "react";
import { AlignLeft, AlignCenter, AlignRight } from "lucide-react";
import Tooltip from "@components/primitive/Tooltip";
import { useEmailBuilderContext } from "@components/builder/context";
import useGlobalStylesStore from "@stores/builder/useGlobalStylesStore";

const AlignmentControls = () => {
  const { textStyle } = useGlobalStylesStore();
  const { editorRef } = useEmailBuilderContext();
  const editor = editorRef.current;

  const type = editor?.state.selection.$from.parent.type.name || "";
  const level = editor?.getAttributes(type)?.level ?? 0;
  const styleKey = type === "paragraph" ? "p" : (`h${level}` as "h1" | "h2" | "h3");
  const localTextAlign = editor?.getAttributes(type)?.textAlign || "";
  const globalTextAlign = textStyle[styleKey]?.textAlign || "";
  const textAlign = localTextAlign || globalTextAlign;

  const onClick = (alignment: string) => {
    if (!editor) return;
    editor.chain().focus().setTextAlign(alignment).run();
  };

  return (
    <div className="flex items-center gap-1 border-l border-r px-2">
      <Tooltip label="Align Left">
        <button
          onClick={() => onClick("left")}
          className={`rounded p-2 hover:bg-gray-100 ${textAlign === "left" && "bg-light200"}`}
        >
          <AlignLeft size={18} />
        </button>
      </Tooltip>
      <Tooltip label="Align Center">
        <button
          onClick={() => onClick("center")}
          className={`rounded p-2 hover:bg-gray-100 ${textAlign === "center" && "bg-light200"}`}
        >
          <AlignCenter size={18} />
        </button>
      </Tooltip>
      <Tooltip label="Align Right">
        <button
          onClick={() => onClick("right")}
          className={`rounded p-2 hover:bg-gray-100 ${textAlign === "right" && "bg-light200"}`}
        >
          <AlignRight size={18} />
        </button>
      </Tooltip>
    </div>
  );
};

export default AlignmentControls;
