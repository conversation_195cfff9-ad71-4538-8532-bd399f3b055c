import useBuilderStore from "@stores/builder/useBuilderStore";
import FormatColorFillIcon from "mdi-react/FormatColorFillIcon";
import PlusCircleIcon from "mdi-react/PlusCircleIcon";
import SidebarButton from "@components/builder/SidebarButton";

export type SidebarOption = "add" | "styles";

const VerticalSidebar = () => {
  const { activeSidebarOption, setActiveSidebarOption, focusedItem } = useBuilderStore();
  const isVisible = !focusedItem;

  if (!isVisible) return null;
  return (
    <div className="flex h-[calc(100vh-64px)] w-[70px] flex-col border-r border-gray-200 bg-light100">
      <SidebarButton
        onClick={() => setActiveSidebarOption("add")}
        active={activeSidebarOption === "add"}
        icon={<PlusCircleIcon className="h-5 w-5" />}
        label="Add"
      />
      <SidebarButton
        onClick={() => setActiveSidebarOption("styles")}
        active={activeSidebarOption === "styles"}
        icon={<FormatColorFillIcon className="h-5 w-5" />}
        label="Styles"
      />
    </div>
  );
};

export default VerticalSidebar;
