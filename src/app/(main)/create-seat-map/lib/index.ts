// Core seat mapping system
export { SeatMapManager } from './seat-map-manager';
export type { SeatMapConfig, SeatMapStats } from './seat-map-manager';

// Rendering system
export { SeatRenderer, PoolableSeat } from './rendering/seat-renderer';
export type { SeatData, SeatRenderOptions, SeatColorScheme } from './rendering/seat-renderer';

// Performance systems
export { ViewportCuller } from './performance/viewport-culling';
export type { ViewportBounds, CullingResult } from './performance/viewport-culling';

export { ObjectPool, KonvaShapePool, globalMemoryPool } from './performance/object-pool';
export type { PoolableObject, PoolableKonvaShape } from './performance/object-pool';

export { PerformanceMonitor, globalPerformanceMonitor } from './performance/performance-monitor';
export type { PerformanceMetrics, PerformanceThresholds } from './performance/performance-monitor';

export { AdvancedMemoryManager, LRUCache, globalMemoryManager } from './performance/memory-manager';
export type { MemoryStats, CacheEntry, CacheOptions } from './performance/memory-manager';

// Spatial indexing
export { QuadTree } from './spatial/quadtree';
export type { Point, Rectangle, QuadTreeObject } from './spatial/quadtree';

// Seat generation
export { ShapeBasedSeatGenerator } from './generation/shape-generator';
export type { 
  ShapeConfig, 
  RectangleConfig, 
  CircleConfig, 
  ArcConfig, 
  PolygonConfig, 
  AnyShapeConfig,
  SeatGenerationOptions 
} from './generation/shape-generator';

// Seat interaction
export { SeatSelector } from './interaction/seat-selector';
export type { 
  SelectionArea, 
  SelectionOptions, 
  SelectionResult 
} from './interaction/seat-selector';

// Labeling system
export { SeatLabelingSystem } from './labeling/seat-labeling';
export type { 
  LabelingScheme, 
  NumberingPattern, 
  LabelingOptions 
} from './labeling/seat-labeling';

// Venue templates
export { VenueTemplateManager } from './templates/venue-templates';
export type { 
  VenueTemplate, 
  VenueSection 
} from './templates/venue-templates';

// Testing and performance
export { PerformanceTester, globalPerformanceTester } from './testing/performance-tester';
export type { 
  PerformanceTestConfig, 
  PerformanceTestResult, 
  StressTestScenario 
} from './testing/performance-tester';

// Configuration
export * from './config';

// Utility functions
export const createHighPerformanceSeatMap = (
  container: HTMLDivElement,
  config: {
    width: number;
    height: number;
    templateId?: string;
    seatCount?: number;
    enablePerformanceMonitoring?: boolean;
  }
) => {
  const { SeatMapManager } = require('./seat-map-manager');
  const { VenueTemplateManager } = require('./templates/venue-templates');
  
  const venueManager = new VenueTemplateManager();
  
  let seats = [];
  if (config.templateId) {
    seats = venueManager.generateVenueSeats(config.templateId);
  } else if (config.seatCount) {
    // Generate grid of seats for testing
    const seatsPerRow = Math.ceil(Math.sqrt(config.seatCount));
    const seatSize = 20;
    const seatGap = 5;
    
    for (let i = 0; i < config.seatCount; i++) {
      const row = Math.floor(i / seatsPerRow);
      const seat = i % seatsPerRow;
      
      seats.push({
        id: `seat-${i}`,
        x: seat * (seatSize + seatGap),
        y: row * (seatSize + seatGap),
        width: seatSize,
        height: seatSize,
        row: row + 1,
        seatNumber: seat + 1,
        state: 'available',
        sectionId: 'MAIN',
        label: `${row + 1}-${seat + 1}`
      });
    }
  }
  
  const seatMapConfig = {
    width: config.width,
    height: config.height,
    initialSeats: seats,
    enablePerformanceMonitoring: config.enablePerformanceMonitoring ?? true
  };
  
  return new SeatMapManager(container, seatMapConfig);
};

// Performance benchmarking utility
export const runPerformanceBenchmark = async (
  seatCounts: number[] = [1000, 5000, 25000, 100000],
  testDuration: number = 10000
) => {
  const { globalPerformanceTester } = require('./testing/performance-tester');
  
  // Create a temporary container for testing
  const container = document.createElement('div');
  container.style.width = '800px';
  container.style.height = '600px';
  container.style.position = 'absolute';
  container.style.top = '-9999px';
  document.body.appendChild(container);
  
  try {
    const seatMapManager = createHighPerformanceSeatMap(container, {
      width: 800,
      height: 600,
      enablePerformanceMonitoring: true
    });
    
    const config = {
      seatCounts,
      testDuration,
      interactionTests: true,
      memoryTests: true,
      renderTests: true
    };
    
    const results = await globalPerformanceTester.runComprehensiveTest(seatMapManager, config);
    
    seatMapManager.destroy();
    return results;
  } finally {
    document.body.removeChild(container);
  }
};

// Quick setup functions for common use cases
export const setupStadiumSeatMap = (container: HTMLDivElement, capacity: number = 50000) => {
  return createHighPerformanceSeatMap(container, {
    width: 1200,
    height: 800,
    templateId: 'stadium-football',
    enablePerformanceMonitoring: true
  });
};

export const setupTheaterSeatMap = (container: HTMLDivElement, capacity: number = 1500) => {
  return createHighPerformanceSeatMap(container, {
    width: 1000,
    height: 700,
    templateId: 'theater-broadway',
    enablePerformanceMonitoring: true
  });
};

export const setupConcertSeatMap = (container: HTMLDivElement, capacity: number = 20000) => {
  return createHighPerformanceSeatMap(container, {
    width: 1100,
    height: 900,
    templateId: 'concert-arena',
    enablePerformanceMonitoring: true
  });
};

export const setupConferenceSeatMap = (container: HTMLDivElement, capacity: number = 500) => {
  return createHighPerformanceSeatMap(container, {
    width: 800,
    height: 600,
    templateId: 'conference-center',
    enablePerformanceMonitoring: true
  });
};
