import Konva from 'konva';
import { SEAT_STATES, LOD_THRESHOLD_1, LOD_THRESHOLD_2, BATCH_SIZE } from '../config';
import { PoolableObject, KonvaShapePool } from '../performance/object-pool';
import { ViewportBounds, CullingResult } from '../performance/viewport-culling';

export interface SeatData {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  row: number;
  seatNumber: number;
  state: keyof typeof SEAT_STATES;
  sectionId: string;
  label?: string;
  price?: number;
  metadata?: any;
}

export interface SeatRenderOptions {
  showLabels: boolean;
  showPrices: boolean;
  colorScheme: SeatColorScheme;
  lodLevel: number;
}

export interface SeatColorScheme {
  available: string;
  selected: string;
  disabled: string;
  sold: string;
  reserved: string;
  hover: string;
  border: string;
}

export class PoolableSeat implements PoolableObject {
  private konvaRect: Konva.Rect;
  private konvaText?: Konva.Text;
  private inUse = false;
  private seatData?: SeatData;

  constructor() {
    this.konvaRect = new Konva.Rect({
      cornerRadius: 2,
      strokeWidth: 1,
      listening: true
    });
  }

  reset(): void {
    this.konvaRect.setAttrs({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
      fill: '',
      stroke: '',
      visible: false
    });
    
    if (this.konvaText) {
      this.konvaText.setAttrs({
        text: '',
        visible: false
      });
    }
    
    this.seatData = undefined;
  }

  isInUse(): boolean {
    return this.inUse;
  }

  setInUse(inUse: boolean): void {
    this.inUse = inUse;
  }

  getKonvaNode(): Konva.Rect {
    return this.konvaRect;
  }

  getTextNode(): Konva.Text | undefined {
    return this.konvaText;
  }

  updateProperties(seatData: SeatData, options: SeatRenderOptions): void {
    this.seatData = seatData;
    const colorScheme = options.colorScheme;
    
    // Update rectangle properties
    this.konvaRect.setAttrs({
      x: seatData.x,
      y: seatData.y,
      width: seatData.width,
      height: seatData.height,
      fill: colorScheme[seatData.state],
      stroke: colorScheme.border,
      visible: true,
      id: seatData.id
    });

    // Handle LOD-based rendering
    if (options.lodLevel === 2) {
      // Minimal rendering for far zoom
      this.konvaRect.strokeWidth(0);
      if (this.konvaText) {
        this.konvaText.visible(false);
      }
    } else if (options.lodLevel === 1) {
      // Simplified rendering
      this.konvaRect.strokeWidth(0.5);
      if (this.konvaText) {
        this.konvaText.visible(false);
      }
    } else {
      // Full detail rendering
      this.konvaRect.strokeWidth(1);
      
      // Add text label if needed and not exists
      if (options.showLabels && seatData.label) {
        if (!this.konvaText) {
          this.konvaText = new Konva.Text({
            fontSize: 10,
            fontFamily: 'Arial',
            fill: 'white',
            align: 'center',
            verticalAlign: 'middle',
            listening: false
          });
        }
        
        this.konvaText.setAttrs({
          x: seatData.x,
          y: seatData.y,
          width: seatData.width,
          height: seatData.height,
          text: seatData.label,
          visible: true
        });
      } else if (this.konvaText) {
        this.konvaText.visible(false);
      }
    }
  }

  getSeatData(): SeatData | undefined {
    return this.seatData;
  }

  setHover(isHover: boolean, colorScheme: SeatColorScheme): void {
    if (!this.seatData) return;
    
    if (isHover) {
      this.konvaRect.fill(colorScheme.hover);
    } else {
      this.konvaRect.fill(colorScheme[this.seatData.state]);
    }
  }
}

export class SeatRenderer {
  private seatPool: KonvaShapePool<PoolableSeat>;
  private layer: Konva.Layer;
  private textLayer: Konva.Layer;
  private batchedUpdates: SeatData[] = [];
  private renderOptions: SeatRenderOptions;
  private onSeatClick?: (seatData: SeatData) => void;
  private onSeatHover?: (seatData: SeatData | null) => void;

  constructor(
    layer: Konva.Layer,
    textLayer: Konva.Layer,
    initialPoolSize: number = 1000,
    maxPoolSize: number = 10000
  ) {
    this.layer = layer;
    this.textLayer = textLayer;
    
    this.seatPool = new KonvaShapePool<PoolableSeat>(
      () => new PoolableSeat(),
      initialPoolSize,
      maxPoolSize
    );

    this.renderOptions = {
      showLabels: true,
      showPrices: false,
      lodLevel: 0,
      colorScheme: {
        available: '#4ade80',
        selected: '#3b82f6',
        disabled: '#9ca3af',
        sold: '#ef4444',
        reserved: '#f59e0b',
        hover: '#06b6d4',
        border: '#374151'
      }
    };

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.layer.on('click', (e) => {
      const target = e.target;
      if (target instanceof Konva.Rect) {
        const seatId = target.id();
        const seat = this.seatPool.getActiveShape(seatId);
        if (seat && this.onSeatClick) {
          const seatData = seat.getSeatData();
          if (seatData) {
            this.onSeatClick(seatData);
          }
        }
      }
    });

    this.layer.on('mouseover', (e) => {
      const target = e.target;
      if (target instanceof Konva.Rect) {
        const seatId = target.id();
        const seat = this.seatPool.getActiveShape(seatId);
        if (seat) {
          seat.setHover(true, this.renderOptions.colorScheme);
          const seatData = seat.getSeatData();
          if (seatData && this.onSeatHover) {
            this.onSeatHover(seatData);
          }
        }
      }
    });

    this.layer.on('mouseout', (e) => {
      const target = e.target;
      if (target instanceof Konva.Rect) {
        const seatId = target.id();
        const seat = this.seatPool.getActiveShape(seatId);
        if (seat) {
          seat.setHover(false, this.renderOptions.colorScheme);
          if (this.onSeatHover) {
            this.onSeatHover(null);
          }
        }
      }
    });
  }

  renderSeats(cullingResult: CullingResult, allSeats: SeatData[]): void {
    // Update LOD level
    this.renderOptions.lodLevel = cullingResult.lodLevel;

    // Get visible seat data
    const visibleSeatIds = new Set(cullingResult.visibleObjects.map(obj => obj.id));
    const visibleSeats = allSeats.filter(seat => visibleSeatIds.has(seat.id));

    // Release seats that are no longer visible
    const activeShapes = this.seatPool.getActiveShapes();
    for (const shape of activeShapes) {
      const seatData = shape.getSeatData();
      if (seatData && !visibleSeatIds.has(seatData.id)) {
        this.layer.remove(shape.getKonvaNode());
        const textNode = shape.getTextNode();
        if (textNode) {
          this.textLayer.remove(textNode);
        }
        this.seatPool.releaseById(seatData.id);
      }
    }

    // Render visible seats in batches
    this.renderSeatsBatched(visibleSeats);
  }

  private renderSeatsBatched(seats: SeatData[]): void {
    const batches = this.createBatches(seats, BATCH_SIZE);
    
    const processBatch = (batchIndex: number) => {
      if (batchIndex >= batches.length) {
        this.layer.batchDraw();
        if (this.renderOptions.showLabels && this.renderOptions.lodLevel === 0) {
          this.textLayer.batchDraw();
        }
        return;
      }

      const batch = batches[batchIndex];
      for (const seatData of batch) {
        this.renderSingleSeat(seatData);
      }

      // Process next batch in next frame
      requestAnimationFrame(() => processBatch(batchIndex + 1));
    };

    processBatch(0);
  }

  private renderSingleSeat(seatData: SeatData): void {
    let seat = this.seatPool.getActiveShape(seatData.id);
    
    if (!seat) {
      seat = this.seatPool.acquireWithId(seatData.id);
      this.layer.add(seat.getKonvaNode());
      
      const textNode = seat.getTextNode();
      if (textNode) {
        this.textLayer.add(textNode);
      }
    }

    seat.updateProperties(seatData, this.renderOptions);
  }

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  updateSeatState(seatId: string, newState: keyof typeof SEAT_STATES): void {
    const seat = this.seatPool.getActiveShape(seatId);
    if (seat) {
      const seatData = seat.getSeatData();
      if (seatData) {
        seatData.state = newState;
        seat.updateProperties(seatData, this.renderOptions);
      }
    }
  }

  updateRenderOptions(options: Partial<SeatRenderOptions>): void {
    this.renderOptions = { ...this.renderOptions, ...options };
    
    // Re-render all active seats with new options
    const activeShapes = this.seatPool.getActiveShapes();
    for (const shape of activeShapes) {
      const seatData = shape.getSeatData();
      if (seatData) {
        shape.updateProperties(seatData, this.renderOptions);
      }
    }
    
    this.layer.batchDraw();
    this.textLayer.batchDraw();
  }

  onSeatClickHandler(callback: (seatData: SeatData) => void): void {
    this.onSeatClick = callback;
  }

  onSeatHoverHandler(callback: (seatData: SeatData | null) => void): void {
    this.onSeatHover = callback;
  }

  clearAllSeats(): void {
    this.seatPool.releaseAllActive();
    this.layer.removeChildren();
    this.textLayer.removeChildren();
    this.layer.batchDraw();
    this.textLayer.batchDraw();
  }

  getStats(): {
    activeSeats: number;
    poolStats: any;
  } {
    return {
      activeSeats: this.seatPool.getActiveCount(),
      poolStats: this.seatPool.getStats()
    };
  }

  destroy(): void {
    this.clearAllSeats();
    this.seatPool.clear();
    this.layer.off('click');
    this.layer.off('mouseover');
    this.layer.off('mouseout');
  }
}
