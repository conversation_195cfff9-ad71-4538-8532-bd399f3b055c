import { QUADTREE_MAX_OBJECTS, QUADTREE_MAX_LEVELS } from '../config';

export interface Point {
  x: number;
  y: number;
}

export interface Rectangle {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface QuadTreeObject {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  data?: any;
}

export class QuadTree {
  private level: number;
  private objects: QuadTreeObject[];
  private bounds: Rectangle;
  private nodes: QuadTree[];

  constructor(bounds: Rectangle, level: number = 0) {
    this.level = level;
    this.objects = [];
    this.bounds = bounds;
    this.nodes = [];
  }

  clear(): void {
    this.objects = [];
    for (const node of this.nodes) {
      node.clear();
    }
    this.nodes = [];
  }

  split(): void {
    const subWidth = this.bounds.width / 2;
    const subHeight = this.bounds.height / 2;
    const x = this.bounds.x;
    const y = this.bounds.y;

    this.nodes[0] = new QuadTree({
      x: x + subWidth,
      y: y,
      width: subWidth,
      height: subHeight
    }, this.level + 1);

    this.nodes[1] = new QuadTree({
      x: x,
      y: y,
      width: subWidth,
      height: subHeight
    }, this.level + 1);

    this.nodes[2] = new QuadTree({
      x: x,
      y: y + subHeight,
      width: subWidth,
      height: subHeight
    }, this.level + 1);

    this.nodes[3] = new QuadTree({
      x: x + subWidth,
      y: y + subHeight,
      width: subWidth,
      height: subHeight
    }, this.level + 1);
  }

  getIndex(obj: QuadTreeObject): number {
    let index = -1;
    const verticalMidpoint = this.bounds.x + (this.bounds.width / 2);
    const horizontalMidpoint = this.bounds.y + (this.bounds.height / 2);

    const topQuadrant = (obj.y < horizontalMidpoint && obj.y + obj.height < horizontalMidpoint);
    const bottomQuadrant = (obj.y > horizontalMidpoint);

    if (obj.x < verticalMidpoint && obj.x + obj.width < verticalMidpoint) {
      if (topQuadrant) {
        index = 1;
      } else if (bottomQuadrant) {
        index = 2;
      }
    } else if (obj.x > verticalMidpoint) {
      if (topQuadrant) {
        index = 0;
      } else if (bottomQuadrant) {
        index = 3;
      }
    }

    return index;
  }

  insert(obj: QuadTreeObject): void {
    if (this.nodes.length > 0) {
      const index = this.getIndex(obj);
      if (index !== -1) {
        this.nodes[index].insert(obj);
        return;
      }
    }

    this.objects.push(obj);

    if (this.objects.length > QUADTREE_MAX_OBJECTS && this.level < QUADTREE_MAX_LEVELS) {
      if (this.nodes.length === 0) {
        this.split();
      }

      let i = 0;
      while (i < this.objects.length) {
        const index = this.getIndex(this.objects[i]);
        if (index !== -1) {
          this.nodes[index].insert(this.objects.splice(i, 1)[0]);
        } else {
          i++;
        }
      }
    }
  }

  retrieve(returnObjects: QuadTreeObject[], obj: Rectangle): QuadTreeObject[] {
    const index = this.getIndex(obj as QuadTreeObject);
    if (index !== -1 && this.nodes.length > 0) {
      this.nodes[index].retrieve(returnObjects, obj);
    }

    returnObjects.push(...this.objects);
    return returnObjects;
  }

  retrieveInBounds(bounds: Rectangle): QuadTreeObject[] {
    const returnObjects: QuadTreeObject[] = [];
    
    if (!this.intersects(this.bounds, bounds)) {
      return returnObjects;
    }

    for (const obj of this.objects) {
      if (this.intersects(obj, bounds)) {
        returnObjects.push(obj);
      }
    }

    if (this.nodes.length > 0) {
      for (const node of this.nodes) {
        returnObjects.push(...node.retrieveInBounds(bounds));
      }
    }

    return returnObjects;
  }

  private intersects(rect1: Rectangle, rect2: Rectangle): boolean {
    return !(rect2.x > rect1.x + rect1.width ||
             rect2.x + rect2.width < rect1.x ||
             rect2.y > rect1.y + rect1.height ||
             rect2.y + rect2.height < rect1.y);
  }

  remove(obj: QuadTreeObject): boolean {
    const index = this.objects.findIndex(o => o.id === obj.id);
    if (index !== -1) {
      this.objects.splice(index, 1);
      return true;
    }

    if (this.nodes.length > 0) {
      const nodeIndex = this.getIndex(obj);
      if (nodeIndex !== -1) {
        return this.nodes[nodeIndex].remove(obj);
      }
    }

    return false;
  }

  getTotalObjects(): number {
    let total = this.objects.length;
    for (const node of this.nodes) {
      total += node.getTotalObjects();
    }
    return total;
  }
}
