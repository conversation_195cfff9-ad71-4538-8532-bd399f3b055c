import { SeatData } from '../rendering/seat-renderer';
import { SEAT_STATES } from '../config';

export interface SelectionArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface SelectionOptions {
  multiSelect: boolean;
  allowDisabledSelection: boolean;
  maxSelections?: number;
  selectionMode: 'single' | 'multi' | 'range' | 'area';
}

export interface SelectionResult {
  selectedSeats: string[];
  deselectedSeats: string[];
  totalSelected: number;
  maxReached: boolean;
}

export class SeatSelector {
  private selectedSeats = new Set<string>();
  private disabledSeats = new Set<string>();
  private reservedSeats = new Set<string>();
  private soldSeats = new Set<string>();
  private allSeats: Map<string, SeatData> = new Map();
  
  private options: SelectionOptions = {
    multiSelect: true,
    allowDisabledSelection: false,
    selectionMode: 'multi'
  };

  private onSelectionChange?: (result: SelectionResult) => void;
  private onMaxSelectionsReached?: () => void;

  constructor(options?: Partial<SelectionOptions>) {
    if (options) {
      this.options = { ...this.options, ...options };
    }
  }

  updateSeats(seats: SeatData[]): void {
    this.allSeats.clear();
    this.selectedSeats.clear();
    this.disabledSeats.clear();
    this.reservedSeats.clear();
    this.soldSeats.clear();

    for (const seat of seats) {
      this.allSeats.set(seat.id, seat);
      
      switch (seat.state) {
        case SEAT_STATES.SELECTED:
          this.selectedSeats.add(seat.id);
          break;
        case SEAT_STATES.DISABLED:
          this.disabledSeats.add(seat.id);
          break;
        case SEAT_STATES.RESERVED:
          this.reservedSeats.add(seat.id);
          break;
        case SEAT_STATES.SOLD:
          this.soldSeats.add(seat.id);
          break;
      }
    }
  }

  selectSeat(seatId: string): SelectionResult {
    const seat = this.allSeats.get(seatId);
    if (!seat) {
      return this.createEmptyResult();
    }

    return this.handleSeatSelection([seatId]);
  }

  selectSeats(seatIds: string[]): SelectionResult {
    return this.handleSeatSelection(seatIds);
  }

  selectSeatsByArea(area: SelectionArea): SelectionResult {
    const seatsInArea = this.getSeatsInArea(area);
    return this.handleSeatSelection(seatsInArea.map(seat => seat.id));
  }

  selectSeatsByRange(startSeatId: string, endSeatId: string): SelectionResult {
    const startSeat = this.allSeats.get(startSeatId);
    const endSeat = this.allSeats.get(endSeatId);
    
    if (!startSeat || !endSeat) {
      return this.createEmptyResult();
    }

    // For range selection, we need to find seats between start and end
    // This assumes seats are in a grid-like arrangement
    const seatsInRange = this.getSeatsInRange(startSeat, endSeat);
    return this.handleSeatSelection(seatsInRange.map(seat => seat.id));
  }

  deselectSeat(seatId: string): SelectionResult {
    const previouslySelected = Array.from(this.selectedSeats);
    this.selectedSeats.delete(seatId);
    
    const result: SelectionResult = {
      selectedSeats: Array.from(this.selectedSeats),
      deselectedSeats: [seatId],
      totalSelected: this.selectedSeats.size,
      maxReached: false
    };

    this.notifySelectionChange(result);
    return result;
  }

  deselectSeats(seatIds: string[]): SelectionResult {
    const deselectedSeats: string[] = [];
    
    for (const seatId of seatIds) {
      if (this.selectedSeats.has(seatId)) {
        this.selectedSeats.delete(seatId);
        deselectedSeats.push(seatId);
      }
    }

    const result: SelectionResult = {
      selectedSeats: Array.from(this.selectedSeats),
      deselectedSeats,
      totalSelected: this.selectedSeats.size,
      maxReached: false
    };

    this.notifySelectionChange(result);
    return result;
  }

  clearSelection(): SelectionResult {
    const deselectedSeats = Array.from(this.selectedSeats);
    this.selectedSeats.clear();

    const result: SelectionResult = {
      selectedSeats: [],
      deselectedSeats,
      totalSelected: 0,
      maxReached: false
    };

    this.notifySelectionChange(result);
    return result;
  }

  toggleSeat(seatId: string): SelectionResult {
    if (this.selectedSeats.has(seatId)) {
      return this.deselectSeat(seatId);
    } else {
      return this.selectSeat(seatId);
    }
  }

  private handleSeatSelection(seatIds: string[]): SelectionResult {
    const previouslySelected = Array.from(this.selectedSeats);
    const newlySelected: string[] = [];
    const deselectedSeats: string[] = [];
    let maxReached = false;

    // Handle single selection mode
    if (this.options.selectionMode === 'single') {
      this.selectedSeats.clear();
      deselectedSeats.push(...previouslySelected);
    }

    for (const seatId of seatIds) {
      const seat = this.allSeats.get(seatId);
      if (!seat) continue;

      // Check if seat can be selected
      if (!this.canSelectSeat(seat)) {
        continue;
      }

      // Check max selections limit
      if (this.options.maxSelections && 
          this.selectedSeats.size >= this.options.maxSelections &&
          !this.selectedSeats.has(seatId)) {
        maxReached = true;
        break;
      }

      // Toggle selection
      if (this.selectedSeats.has(seatId)) {
        if (this.options.multiSelect) {
          this.selectedSeats.delete(seatId);
          deselectedSeats.push(seatId);
        }
      } else {
        this.selectedSeats.add(seatId);
        newlySelected.push(seatId);
      }
    }

    const result: SelectionResult = {
      selectedSeats: Array.from(this.selectedSeats),
      deselectedSeats,
      totalSelected: this.selectedSeats.size,
      maxReached
    };

    if (maxReached && this.onMaxSelectionsReached) {
      this.onMaxSelectionsReached();
    }

    this.notifySelectionChange(result);
    return result;
  }

  private canSelectSeat(seat: SeatData): boolean {
    // Can't select sold seats
    if (seat.state === SEAT_STATES.SOLD) {
      return false;
    }

    // Can't select reserved seats (unless specifically allowed)
    if (seat.state === SEAT_STATES.RESERVED) {
      return false;
    }

    // Check if disabled seats can be selected
    if (seat.state === SEAT_STATES.DISABLED && !this.options.allowDisabledSelection) {
      return false;
    }

    return true;
  }

  private getSeatsInArea(area: SelectionArea): SeatData[] {
    const seatsInArea: SeatData[] = [];
    
    for (const seat of this.allSeats.values()) {
      if (this.isSeatInArea(seat, area)) {
        seatsInArea.push(seat);
      }
    }

    return seatsInArea;
  }

  private isSeatInArea(seat: SeatData, area: SelectionArea): boolean {
    return seat.x >= area.x &&
           seat.x + seat.width <= area.x + area.width &&
           seat.y >= area.y &&
           seat.y + seat.height <= area.y + area.height;
  }

  private getSeatsInRange(startSeat: SeatData, endSeat: SeatData): SeatData[] {
    const seatsInRange: SeatData[] = [];
    
    // Determine the bounding box of the range
    const minX = Math.min(startSeat.x, endSeat.x);
    const maxX = Math.max(startSeat.x + startSeat.width, endSeat.x + endSeat.width);
    const minY = Math.min(startSeat.y, endSeat.y);
    const maxY = Math.max(startSeat.y + startSeat.height, endSeat.y + endSeat.height);

    // Find all seats within the bounding box
    for (const seat of this.allSeats.values()) {
      if (seat.x >= minX && seat.x + seat.width <= maxX &&
          seat.y >= minY && seat.y + seat.height <= maxY) {
        
        // Additional logic for range selection (e.g., same row, consecutive seats)
        if (this.isSeatInRange(seat, startSeat, endSeat)) {
          seatsInRange.push(seat);
        }
      }
    }

    return seatsInRange;
  }

  private isSeatInRange(seat: SeatData, startSeat: SeatData, endSeat: SeatData): boolean {
    // For now, include all seats in the bounding box
    // This can be enhanced with more sophisticated range logic
    return true;
  }

  private createEmptyResult(): SelectionResult {
    return {
      selectedSeats: Array.from(this.selectedSeats),
      deselectedSeats: [],
      totalSelected: this.selectedSeats.size,
      maxReached: false
    };
  }

  private notifySelectionChange(result: SelectionResult): void {
    if (this.onSelectionChange) {
      this.onSelectionChange(result);
    }
  }

  // Public getters
  getSelectedSeats(): string[] {
    return Array.from(this.selectedSeats);
  }

  getSelectedSeatData(): SeatData[] {
    return Array.from(this.selectedSeats)
      .map(id => this.allSeats.get(id))
      .filter((seat): seat is SeatData => seat !== undefined);
  }

  isSelected(seatId: string): boolean {
    return this.selectedSeats.has(seatId);
  }

  getSelectionCount(): number {
    return this.selectedSeats.size;
  }

  canSelectMore(): boolean {
    if (!this.options.maxSelections) return true;
    return this.selectedSeats.size < this.options.maxSelections;
  }

  getRemainingSelections(): number {
    if (!this.options.maxSelections) return Infinity;
    return Math.max(0, this.options.maxSelections - this.selectedSeats.size);
  }

  // Configuration
  updateOptions(options: Partial<SelectionOptions>): void {
    this.options = { ...this.options, ...options };
  }

  getOptions(): SelectionOptions {
    return { ...this.options };
  }

  // Event handlers
  onSelectionChangeHandler(callback: (result: SelectionResult) => void): void {
    this.onSelectionChange = callback;
  }

  onMaxSelectionsReachedHandler(callback: () => void): void {
    this.onMaxSelectionsReached = callback;
  }

  // Seat state management
  disableSeats(seatIds: string[]): void {
    for (const seatId of seatIds) {
      this.disabledSeats.add(seatId);
      this.selectedSeats.delete(seatId); // Remove from selection
      
      const seat = this.allSeats.get(seatId);
      if (seat) {
        seat.state = SEAT_STATES.DISABLED;
      }
    }
  }

  enableSeats(seatIds: string[]): void {
    for (const seatId of seatIds) {
      this.disabledSeats.delete(seatId);
      
      const seat = this.allSeats.get(seatId);
      if (seat) {
        seat.state = SEAT_STATES.AVAILABLE;
      }
    }
  }

  markSeatsAsSold(seatIds: string[]): void {
    for (const seatId of seatIds) {
      this.soldSeats.add(seatId);
      this.selectedSeats.delete(seatId);
      
      const seat = this.allSeats.get(seatId);
      if (seat) {
        seat.state = SEAT_STATES.SOLD;
      }
    }
  }

  markSeatsAsReserved(seatIds: string[]): void {
    for (const seatId of seatIds) {
      this.reservedSeats.add(seatId);
      this.selectedSeats.delete(seatId);
      
      const seat = this.allSeats.get(seatId);
      if (seat) {
        seat.state = SEAT_STATES.RESERVED;
      }
    }
  }
}
