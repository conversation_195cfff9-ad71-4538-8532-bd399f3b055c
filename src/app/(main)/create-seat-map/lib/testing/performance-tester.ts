import { SeatData } from '../rendering/seat-renderer';
import { SeatMapManager } from '../seat-map-manager';
import { globalPerformanceMonitor, PerformanceMetrics } from '../performance/performance-monitor';
import { globalMemoryManager } from '../performance/memory-manager';

export interface PerformanceTestConfig {
  seatCounts: number[];
  testDuration: number; // in milliseconds
  interactionTests: boolean;
  memoryTests: boolean;
  renderTests: boolean;
}

export interface PerformanceTestResult {
  seatCount: number;
  averageFPS: number;
  minFPS: number;
  maxFPS: number;
  averageRenderTime: number;
  maxRenderTime: number;
  memoryUsage: number;
  peakMemoryUsage: number;
  interactionLatency: number;
  cullingEfficiency: number;
  lodTransitions: number;
  testDuration: number;
  passed: boolean;
  issues: string[];
}

export interface StressTestScenario {
  name: string;
  description: string;
  seatCount: number;
  interactions: number;
  zoomLevels: number[];
  panDistance: number;
  expectedMinFPS: number;
}

export class PerformanceTester {
  private testResults: PerformanceTestResult[] = [];
  private isRunning = false;

  constructor() {}

  async runComprehensiveTest(
    seatMapManager: SeatMapManager,
    config: PerformanceTestConfig
  ): Promise<PerformanceTestResult[]> {
    if (this.isRunning) {
      throw new Error('Performance test is already running');
    }

    this.isRunning = true;
    this.testResults = [];

    try {
      for (const seatCount of config.seatCounts) {
        console.log(`Starting performance test with ${seatCount} seats...`);
        
        const result = await this.runSingleTest(seatMapManager, seatCount, config);
        this.testResults.push(result);
        
        // Brief pause between tests
        await this.sleep(1000);
      }

      return this.testResults;
    } finally {
      this.isRunning = false;
    }
  }

  private async runSingleTest(
    seatMapManager: SeatMapManager,
    seatCount: number,
    config: PerformanceTestConfig
  ): Promise<PerformanceTestResult> {
    const startTime = Date.now();
    const metrics: PerformanceMetrics[] = [];
    const issues: string[] = [];

    // Generate test seats
    const seats = this.generateTestSeats(seatCount);
    seatMapManager.loadSeats(seats);

    // Start monitoring
    globalPerformanceMonitor.startMonitoring();
    const initialMemory = globalMemoryManager.getMemoryStats();

    // Collect metrics during test
    const metricsInterval = setInterval(() => {
      const currentMetrics = globalPerformanceMonitor.getMetrics();
      metrics.push({ ...currentMetrics });
    }, 100); // Collect every 100ms

    try {
      // Run different test phases
      if (config.renderTests) {
        await this.runRenderTests(seatMapManager, config.testDuration / 4);
      }

      if (config.interactionTests) {
        await this.runInteractionTests(seatMapManager, config.testDuration / 4);
      }

      if (config.memoryTests) {
        await this.runMemoryTests(seatMapManager, config.testDuration / 4);
      }

      // Additional stress test
      await this.runStressTest(seatMapManager, config.testDuration / 4);

    } finally {
      clearInterval(metricsInterval);
      globalPerformanceMonitor.stopMonitoring();
    }

    const endTime = Date.now();
    const finalMemory = globalMemoryManager.getMemoryStats();

    // Analyze results
    return this.analyzeTestResults(
      seatCount,
      metrics,
      initialMemory,
      finalMemory,
      endTime - startTime,
      issues
    );
  }

  private async runRenderTests(seatMapManager: SeatMapManager, duration: number): Promise<void> {
    const endTime = Date.now() + duration;
    
    while (Date.now() < endTime) {
      // Simulate zoom operations
      const zoomLevels = [0.1, 0.5, 1.0, 2.0, 5.0];
      for (const zoom of zoomLevels) {
        // Simulate zoom change (would need to be implemented in SeatMapManager)
        await this.sleep(100);
      }
      
      await this.sleep(50);
    }
  }

  private async runInteractionTests(seatMapManager: SeatMapManager, duration: number): Promise<void> {
    const endTime = Date.now() + duration;
    const stats = seatMapManager.getStats();
    
    while (Date.now() < endTime) {
      // Simulate seat selections
      const randomSeats = this.generateRandomSeatIds(Math.min(100, stats.totalSeats));
      
      const startTime = performance.now();
      seatMapManager.selectSeats(randomSeats);
      const selectionTime = performance.now() - startTime;
      
      if (selectionTime > 16.67) { // More than one frame at 60fps
        console.warn(`Slow selection: ${selectionTime.toFixed(2)}ms`);
      }
      
      await this.sleep(100);
      
      seatMapManager.deselectSeats(randomSeats);
      await this.sleep(100);
    }
  }

  private async runMemoryTests(seatMapManager: SeatMapManager, duration: number): Promise<void> {
    const endTime = Date.now() + duration;
    
    while (Date.now() < endTime) {
      // Force garbage collection if available
      if ((window as any).gc) {
        (window as any).gc();
      }
      
      // Monitor memory usage
      const memoryStats = globalMemoryManager.getMemoryStats();
      if (memoryStats.currentUsage > 200 * 1024 * 1024) { // 200MB threshold
        console.warn(`High memory usage: ${(memoryStats.currentUsage / 1024 / 1024).toFixed(2)}MB`);
      }
      
      await this.sleep(1000);
    }
  }

  private async runStressTest(seatMapManager: SeatMapManager, duration: number): Promise<void> {
    const endTime = Date.now() + duration;
    
    while (Date.now() < endTime) {
      // Rapid seat state changes
      const stats = seatMapManager.getStats();
      const randomSeats = this.generateRandomSeatIds(Math.min(1000, stats.totalSeats));
      
      seatMapManager.selectSeats(randomSeats.slice(0, 500));
      seatMapManager.disableSeats(randomSeats.slice(500, 750));
      
      await this.sleep(50);
      
      seatMapManager.deselectSeats(randomSeats.slice(0, 500));
      seatMapManager.enableSeats(randomSeats.slice(500, 750));
      
      await this.sleep(50);
    }
  }

  private analyzeTestResults(
    seatCount: number,
    metrics: PerformanceMetrics[],
    initialMemory: any,
    finalMemory: any,
    testDuration: number,
    issues: string[]
  ): PerformanceTestResult {
    if (metrics.length === 0) {
      return {
        seatCount,
        averageFPS: 0,
        minFPS: 0,
        maxFPS: 0,
        averageRenderTime: 0,
        maxRenderTime: 0,
        memoryUsage: 0,
        peakMemoryUsage: 0,
        interactionLatency: 0,
        cullingEfficiency: 0,
        lodTransitions: 0,
        testDuration,
        passed: false,
        issues: ['No metrics collected']
      };
    }

    const fps = metrics.map(m => m.fps).filter(f => f > 0);
    const renderTimes = metrics.map(m => m.renderTime).filter(t => t > 0);
    const memoryUsages = metrics.map(m => m.memoryUsage).filter(m => m > 0);

    const averageFPS = fps.reduce((a, b) => a + b, 0) / fps.length;
    const minFPS = Math.min(...fps);
    const maxFPS = Math.max(...fps);
    
    const averageRenderTime = renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length;
    const maxRenderTime = Math.max(...renderTimes);
    
    const averageMemoryUsage = memoryUsages.reduce((a, b) => a + b, 0) / memoryUsages.length;
    const peakMemoryUsage = Math.max(...memoryUsages);

    // Calculate culling efficiency
    const cullingMetrics = metrics.filter(m => m.totalObjects > 0);
    const cullingEfficiency = cullingMetrics.length > 0 
      ? cullingMetrics.reduce((sum, m) => sum + (m.culledObjects / m.totalObjects), 0) / cullingMetrics.length * 100
      : 0;

    // Count LOD transitions
    let lodTransitions = 0;
    for (let i = 1; i < metrics.length; i++) {
      if (metrics[i].lodLevel !== metrics[i - 1].lodLevel) {
        lodTransitions++;
      }
    }

    // Determine if test passed
    const minAcceptableFPS = seatCount > 50000 ? 30 : 45;
    const maxAcceptableRenderTime = 16.67; // 60fps target
    const maxAcceptableMemory = 500 * 1024 * 1024; // 500MB

    const passed = averageFPS >= minAcceptableFPS && 
                   averageRenderTime <= maxAcceptableRenderTime &&
                   peakMemoryUsage <= maxAcceptableMemory;

    // Collect issues
    if (averageFPS < minAcceptableFPS) {
      issues.push(`Low average FPS: ${averageFPS.toFixed(1)} (expected >= ${minAcceptableFPS})`);
    }
    if (minFPS < 20) {
      issues.push(`Very low minimum FPS: ${minFPS.toFixed(1)}`);
    }
    if (averageRenderTime > maxAcceptableRenderTime) {
      issues.push(`High render time: ${averageRenderTime.toFixed(2)}ms (expected <= ${maxAcceptableRenderTime}ms)`);
    }
    if (peakMemoryUsage > maxAcceptableMemory) {
      issues.push(`High memory usage: ${(peakMemoryUsage / 1024 / 1024).toFixed(2)}MB`);
    }

    return {
      seatCount,
      averageFPS,
      minFPS,
      maxFPS,
      averageRenderTime,
      maxRenderTime,
      memoryUsage: averageMemoryUsage,
      peakMemoryUsage,
      interactionLatency: averageRenderTime, // Approximation
      cullingEfficiency,
      lodTransitions,
      testDuration,
      passed,
      issues
    };
  }

  private generateTestSeats(count: number): SeatData[] {
    const seats: SeatData[] = [];
    const seatsPerRow = Math.ceil(Math.sqrt(count));
    const seatSize = 20;
    const seatGap = 5;

    for (let i = 0; i < count; i++) {
      const row = Math.floor(i / seatsPerRow);
      const seat = i % seatsPerRow;

      seats.push({
        id: `test-${i}`,
        x: seat * (seatSize + seatGap),
        y: row * (seatSize + seatGap),
        width: seatSize,
        height: seatSize,
        row: row + 1,
        seatNumber: seat + 1,
        state: 'available',
        sectionId: 'TEST',
        label: `${row + 1}-${seat + 1}`
      });
    }

    return seats;
  }

  private generateRandomSeatIds(count: number): string[] {
    const ids: string[] = [];
    for (let i = 0; i < count; i++) {
      ids.push(`test-${Math.floor(Math.random() * 100000)}`);
    }
    return ids;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Predefined stress test scenarios
  getStressTestScenarios(): StressTestScenario[] {
    return [
      {
        name: 'Small Venue',
        description: 'Theater or small conference room',
        seatCount: 500,
        interactions: 50,
        zoomLevels: [0.5, 1.0, 2.0],
        panDistance: 200,
        expectedMinFPS: 55
      },
      {
        name: 'Medium Venue',
        description: 'Concert hall or sports arena',
        seatCount: 5000,
        interactions: 200,
        zoomLevels: [0.2, 0.5, 1.0, 2.0],
        panDistance: 500,
        expectedMinFPS: 50
      },
      {
        name: 'Large Venue',
        description: 'Stadium or large arena',
        seatCount: 25000,
        interactions: 500,
        zoomLevels: [0.1, 0.3, 0.7, 1.5],
        panDistance: 1000,
        expectedMinFPS: 40
      },
      {
        name: 'Mega Venue',
        description: 'Massive stadium or convention center',
        seatCount: 100000,
        interactions: 1000,
        zoomLevels: [0.05, 0.2, 0.5, 1.0],
        panDistance: 2000,
        expectedMinFPS: 30
      },
      {
        name: 'Extreme Stress Test',
        description: 'Maximum capacity test',
        seatCount: 1000000,
        interactions: 2000,
        zoomLevels: [0.01, 0.1, 0.3],
        panDistance: 5000,
        expectedMinFPS: 20
      }
    ];
  }

  // Generate performance report
  generateReport(): string {
    if (this.testResults.length === 0) {
      return 'No test results available';
    }

    let report = 'Performance Test Report\n';
    report += '======================\n\n';

    const passedTests = this.testResults.filter(r => r.passed).length;
    const totalTests = this.testResults.length;

    report += `Overall Results: ${passedTests}/${totalTests} tests passed\n\n`;

    for (const result of this.testResults) {
      report += `Seat Count: ${result.seatCount.toLocaleString()}\n`;
      report += `Status: ${result.passed ? 'PASSED' : 'FAILED'}\n`;
      report += `Average FPS: ${result.averageFPS.toFixed(1)}\n`;
      report += `Min FPS: ${result.minFPS.toFixed(1)}\n`;
      report += `Average Render Time: ${result.averageRenderTime.toFixed(2)}ms\n`;
      report += `Peak Memory: ${(result.peakMemoryUsage / 1024 / 1024).toFixed(2)}MB\n`;
      report += `Culling Efficiency: ${result.cullingEfficiency.toFixed(1)}%\n`;
      report += `LOD Transitions: ${result.lodTransitions}\n`;
      
      if (result.issues.length > 0) {
        report += `Issues:\n`;
        for (const issue of result.issues) {
          report += `  - ${issue}\n`;
        }
      }
      
      report += '\n';
    }

    return report;
  }

  getTestResults(): PerformanceTestResult[] {
    return [...this.testResults];
  }

  isTestRunning(): boolean {
    return this.isRunning;
  }
}

// Global performance tester instance
export const globalPerformanceTester = new PerformanceTester();
