import { VIEWPORT_BUFFER, MAX_VISIBLE_SEATS } from '../config';
import { QuadTree, Rectangle, QuadTreeObject } from '../spatial/quadtree';

export interface ViewportBounds {
  x: number;
  y: number;
  width: number;
  height: number;
  scale: number;
}

export interface CullingResult {
  visibleObjects: QuadTreeObject[];
  totalObjects: number;
  culledObjects: number;
  lodLevel: number;
}

export class ViewportCuller {
  private quadTree: QuadTree;
  private lastViewport: ViewportBounds | null = null;
  private cachedResult: CullingResult | null = null;
  private cacheThreshold = 10; // Minimum pixel movement to invalidate cache

  constructor(bounds: Rectangle) {
    this.quadTree = new QuadTree(bounds);
  }

  updateSpatialIndex(objects: QuadTreeObject[]): void {
    this.quadTree.clear();
    for (const obj of objects) {
      this.quadTree.insert(obj);
    }
    this.invalidateCache();
  }

  addObject(obj: QuadTreeObject): void {
    this.quadTree.insert(obj);
    this.invalidateCache();
  }

  removeObject(obj: QuadTreeObject): void {
    this.quadTree.remove(obj);
    this.invalidateCache();
  }

  cullObjects(viewport: ViewportBounds): CullingResult {
    // Check if we can use cached result
    if (this.canUseCachedResult(viewport)) {
      return this.cachedResult!;
    }

    const expandedViewport: Rectangle = {
      x: viewport.x - VIEWPORT_BUFFER,
      y: viewport.y - VIEWPORT_BUFFER,
      width: viewport.width + (VIEWPORT_BUFFER * 2),
      height: viewport.height + (VIEWPORT_BUFFER * 2)
    };

    const visibleObjects = this.quadTree.retrieveInBounds(expandedViewport);
    const totalObjects = this.quadTree.getTotalObjects();
    const culledObjects = totalObjects - visibleObjects.length;

    // Determine LOD level based on scale and object count
    const lodLevel = this.calculateLODLevel(viewport.scale, visibleObjects.length);

    // If we have too many visible objects, apply additional culling
    let finalVisibleObjects = visibleObjects;
    if (visibleObjects.length > MAX_VISIBLE_SEATS) {
      finalVisibleObjects = this.applyAdditionalCulling(visibleObjects, viewport);
    }

    const result: CullingResult = {
      visibleObjects: finalVisibleObjects,
      totalObjects,
      culledObjects: totalObjects - finalVisibleObjects.length,
      lodLevel
    };

    // Cache the result
    this.lastViewport = { ...viewport };
    this.cachedResult = result;

    return result;
  }

  private canUseCachedResult(viewport: ViewportBounds): boolean {
    if (!this.lastViewport || !this.cachedResult) {
      return false;
    }

    const deltaX = Math.abs(viewport.x - this.lastViewport.x);
    const deltaY = Math.abs(viewport.y - this.lastViewport.y);
    const deltaScale = Math.abs(viewport.scale - this.lastViewport.scale);

    return deltaX < this.cacheThreshold && 
           deltaY < this.cacheThreshold && 
           deltaScale < 0.01;
  }

  private calculateLODLevel(scale: number, objectCount: number): number {
    // LOD 0: Full detail
    // LOD 1: Simplified seats
    // LOD 2: Section outlines only
    
    if (scale < 0.2 || objectCount > MAX_VISIBLE_SEATS * 2) {
      return 2; // Section outlines only
    } else if (scale < 0.5 || objectCount > MAX_VISIBLE_SEATS) {
      return 1; // Simplified seats
    }
    return 0; // Full detail
  }

  private applyAdditionalCulling(objects: QuadTreeObject[], viewport: ViewportBounds): QuadTreeObject[] {
    // Sort by distance from viewport center and take the closest ones
    const centerX = viewport.x + viewport.width / 2;
    const centerY = viewport.y + viewport.height / 2;

    const objectsWithDistance = objects.map(obj => ({
      obj,
      distance: Math.sqrt(
        Math.pow(obj.x + obj.width / 2 - centerX, 2) + 
        Math.pow(obj.y + obj.height / 2 - centerY, 2)
      )
    }));

    objectsWithDistance.sort((a, b) => a.distance - b.distance);
    
    return objectsWithDistance
      .slice(0, MAX_VISIBLE_SEATS)
      .map(item => item.obj);
  }

  private invalidateCache(): void {
    this.lastViewport = null;
    this.cachedResult = null;
  }

  // Get statistics for debugging
  getStats(): {
    totalObjects: number;
    quadTreeDepth: number;
    cacheHit: boolean;
  } {
    return {
      totalObjects: this.quadTree.getTotalObjects(),
      quadTreeDepth: this.getQuadTreeDepth(this.quadTree),
      cacheHit: this.cachedResult !== null
    };
  }

  private getQuadTreeDepth(node: QuadTree, currentDepth: number = 0): number {
    const nodes = (node as any).nodes;
    if (!nodes || nodes.length === 0) {
      return currentDepth;
    }

    let maxDepth = currentDepth;
    for (const childNode of nodes) {
      const childDepth = this.getQuadTreeDepth(childNode, currentDepth + 1);
      maxDepth = Math.max(maxDepth, childDepth);
    }

    return maxDepth;
  }

  // Update viewport bounds for the quadtree
  updateBounds(bounds: Rectangle): void {
    const objects: QuadTreeObject[] = [];
    this.getAllObjects(this.quadTree, objects);
    
    this.quadTree = new QuadTree(bounds);
    this.updateSpatialIndex(objects);
  }

  private getAllObjects(node: QuadTree, objects: QuadTreeObject[]): void {
    const nodeObjects = (node as any).objects;
    const childNodes = (node as any).nodes;
    
    objects.push(...nodeObjects);
    
    if (childNodes && childNodes.length > 0) {
      for (const childNode of childNodes) {
        this.getAllObjects(childNode, objects);
      }
    }
  }
}
