export interface PoolableObject {
  reset(): void;
  isInUse(): boolean;
  setInUse(inUse: boolean): void;
}

export class ObjectPool<T extends PoolableObject> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn?: (obj: T) => void;
  private maxSize: number;
  private currentSize: number = 0;

  constructor(
    createFn: () => T,
    initialSize: number = 10,
    maxSize: number = 1000,
    resetFn?: (obj: T) => void
  ) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    this.maxSize = maxSize;

    // Pre-populate the pool
    for (let i = 0; i < initialSize; i++) {
      const obj = this.createFn();
      obj.setInUse(false);
      this.pool.push(obj);
      this.currentSize++;
    }
  }

  acquire(): T {
    let obj: T;

    if (this.pool.length > 0) {
      obj = this.pool.pop()!;
    } else {
      obj = this.createFn();
      this.currentSize++;
    }

    obj.setInUse(true);
    return obj;
  }

  release(obj: T): void {
    if (!obj.isInUse()) {
      console.warn('Attempting to release an object that is not in use');
      return;
    }

    obj.reset();
    if (this.resetFn) {
      this.resetFn(obj);
    }
    obj.setInUse(false);

    if (this.pool.length < this.maxSize) {
      this.pool.push(obj);
    } else {
      this.currentSize--;
    }
  }

  releaseAll(objects: T[]): void {
    for (const obj of objects) {
      this.release(obj);
    }
  }

  getStats(): {
    poolSize: number;
    totalCreated: number;
    inUse: number;
    available: number;
  } {
    return {
      poolSize: this.pool.length,
      totalCreated: this.currentSize,
      inUse: this.currentSize - this.pool.length,
      available: this.pool.length
    };
  }

  clear(): void {
    this.pool = [];
    this.currentSize = 0;
  }

  resize(newMaxSize: number): void {
    this.maxSize = newMaxSize;
    
    // If current pool is larger than new max size, trim it
    if (this.pool.length > newMaxSize) {
      const excess = this.pool.length - newMaxSize;
      this.pool.splice(newMaxSize, excess);
      this.currentSize -= excess;
    }
  }
}

// Specific pool for Konva shapes
export interface PoolableKonvaShape extends PoolableObject {
  getKonvaNode(): any;
  updateProperties(props: any): void;
}

export class KonvaShapePool<T extends PoolableKonvaShape> extends ObjectPool<T> {
  private activeShapes = new Map<string, T>();

  acquireWithId(id: string): T {
    const existing = this.activeShapes.get(id);
    if (existing) {
      return existing;
    }

    const shape = this.acquire();
    this.activeShapes.set(id, shape);
    return shape;
  }

  releaseById(id: string): void {
    const shape = this.activeShapes.get(id);
    if (shape) {
      this.activeShapes.delete(id);
      this.release(shape);
    }
  }

  releaseAllActive(): void {
    for (const [id, shape] of this.activeShapes) {
      this.release(shape);
    }
    this.activeShapes.clear();
  }

  getActiveShape(id: string): T | undefined {
    return this.activeShapes.get(id);
  }

  getActiveShapes(): T[] {
    return Array.from(this.activeShapes.values());
  }

  getActiveCount(): number {
    return this.activeShapes.size;
  }
}

// Memory pool for frequently allocated objects
export class MemoryPool {
  private pools = new Map<string, ObjectPool<any>>();

  createPool<T extends PoolableObject>(
    name: string,
    createFn: () => T,
    initialSize: number = 10,
    maxSize: number = 1000,
    resetFn?: (obj: T) => void
  ): ObjectPool<T> {
    const pool = new ObjectPool(createFn, initialSize, maxSize, resetFn);
    this.pools.set(name, pool);
    return pool;
  }

  getPool<T extends PoolableObject>(name: string): ObjectPool<T> | undefined {
    return this.pools.get(name);
  }

  clearAll(): void {
    for (const pool of this.pools.values()) {
      pool.clear();
    }
    this.pools.clear();
  }

  getGlobalStats(): {
    totalPools: number;
    totalObjects: number;
    totalInUse: number;
    totalAvailable: number;
  } {
    let totalObjects = 0;
    let totalInUse = 0;
    let totalAvailable = 0;

    for (const pool of this.pools.values()) {
      const stats = pool.getStats();
      totalObjects += stats.totalCreated;
      totalInUse += stats.inUse;
      totalAvailable += stats.available;
    }

    return {
      totalPools: this.pools.size,
      totalObjects,
      totalInUse,
      totalAvailable
    };
  }
}

// Global memory pool instance
export const globalMemoryPool = new MemoryPool();
