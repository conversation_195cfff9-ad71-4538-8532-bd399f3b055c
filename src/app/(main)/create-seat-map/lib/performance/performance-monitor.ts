export interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  renderTime: number;
  updateTime: number;
  memoryUsage: number;
  visibleObjects: number;
  totalObjects: number;
  culledObjects: number;
  lodLevel: number;
  cacheHitRate: number;
}

export interface PerformanceThresholds {
  minFPS: number;
  maxFrameTime: number;
  maxRenderTime: number;
  maxMemoryUsage: number;
}

export class PerformanceMonitor {
  private metrics: PerformanceMetrics;
  private thresholds: PerformanceThresholds;
  private frameCount = 0;
  private lastTime = 0;
  private fpsHistory: number[] = [];
  private frameTimeHistory: number[] = [];
  private renderTimeHistory: number[] = [];
  private updateTimeHistory: number[] = [];
  private historySize = 60; // Keep 60 frames of history
  private isMonitoring = false;
  private callbacks: ((metrics: PerformanceMetrics) => void)[] = [];
  private warningCallbacks: ((issue: string, value: number) => void)[] = [];

  constructor(thresholds: PerformanceThresholds = {
    minFPS: 30,
    maxFrameTime: 33.33, // ~30 FPS
    maxRenderTime: 16.67, // ~60 FPS
    maxMemoryUsage: 100 * 1024 * 1024 // 100MB
  }) {
    this.thresholds = thresholds;
    this.metrics = {
      fps: 0,
      frameTime: 0,
      renderTime: 0,
      updateTime: 0,
      memoryUsage: 0,
      visibleObjects: 0,
      totalObjects: 0,
      culledObjects: 0,
      lodLevel: 0,
      cacheHitRate: 0
    };
  }

  startMonitoring(): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.lastTime = performance.now();
    this.frameCount = 0;
    this.monitorFrame();
  }

  stopMonitoring(): void {
    this.isMonitoring = false;
  }

  private monitorFrame(): void {
    if (!this.isMonitoring) return;

    const currentTime = performance.now();
    const deltaTime = currentTime - this.lastTime;
    
    this.frameCount++;
    
    // Calculate FPS
    if (deltaTime >= 1000) { // Update every second
      this.metrics.fps = (this.frameCount * 1000) / deltaTime;
      this.frameCount = 0;
      this.lastTime = currentTime;
      
      // Add to history
      this.fpsHistory.push(this.metrics.fps);
      if (this.fpsHistory.length > this.historySize) {
        this.fpsHistory.shift();
      }
    }

    // Calculate frame time
    this.metrics.frameTime = deltaTime;
    this.frameTimeHistory.push(deltaTime);
    if (this.frameTimeHistory.length > this.historySize) {
      this.frameTimeHistory.shift();
    }

    // Get memory usage if available
    if ((performance as any).memory) {
      this.metrics.memoryUsage = (performance as any).memory.usedJSHeapSize;
    }

    // Check thresholds and trigger warnings
    this.checkThresholds();

    // Notify callbacks
    this.notifyCallbacks();

    requestAnimationFrame(() => this.monitorFrame());
  }

  measureRenderTime<T>(renderFn: () => T): T {
    const startTime = performance.now();
    const result = renderFn();
    const endTime = performance.now();
    
    this.metrics.renderTime = endTime - startTime;
    this.renderTimeHistory.push(this.metrics.renderTime);
    if (this.renderTimeHistory.length > this.historySize) {
      this.renderTimeHistory.shift();
    }
    
    return result;
  }

  measureUpdateTime<T>(updateFn: () => T): T {
    const startTime = performance.now();
    const result = updateFn();
    const endTime = performance.now();
    
    this.metrics.updateTime = endTime - startTime;
    this.updateTimeHistory.push(this.metrics.updateTime);
    if (this.updateTimeHistory.length > this.historySize) {
      this.updateTimeHistory.shift();
    }
    
    return result;
  }

  updateObjectMetrics(visibleObjects: number, totalObjects: number, culledObjects: number, lodLevel: number): void {
    this.metrics.visibleObjects = visibleObjects;
    this.metrics.totalObjects = totalObjects;
    this.metrics.culledObjects = culledObjects;
    this.metrics.lodLevel = lodLevel;
  }

  updateCacheHitRate(hits: number, total: number): void {
    this.metrics.cacheHitRate = total > 0 ? (hits / total) * 100 : 0;
  }

  private checkThresholds(): void {
    if (this.metrics.fps < this.thresholds.minFPS) {
      this.triggerWarning('Low FPS', this.metrics.fps);
    }
    
    if (this.metrics.frameTime > this.thresholds.maxFrameTime) {
      this.triggerWarning('High frame time', this.metrics.frameTime);
    }
    
    if (this.metrics.renderTime > this.thresholds.maxRenderTime) {
      this.triggerWarning('High render time', this.metrics.renderTime);
    }
    
    if (this.metrics.memoryUsage > this.thresholds.maxMemoryUsage) {
      this.triggerWarning('High memory usage', this.metrics.memoryUsage);
    }
  }

  private triggerWarning(issue: string, value: number): void {
    for (const callback of this.warningCallbacks) {
      callback(issue, value);
    }
  }

  private notifyCallbacks(): void {
    for (const callback of this.callbacks) {
      callback({ ...this.metrics });
    }
  }

  onMetricsUpdate(callback: (metrics: PerformanceMetrics) => void): void {
    this.callbacks.push(callback);
  }

  onPerformanceWarning(callback: (issue: string, value: number) => void): void {
    this.warningCallbacks.push(callback);
  }

  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  getAverageMetrics(): {
    avgFPS: number;
    avgFrameTime: number;
    avgRenderTime: number;
    avgUpdateTime: number;
  } {
    return {
      avgFPS: this.calculateAverage(this.fpsHistory),
      avgFrameTime: this.calculateAverage(this.frameTimeHistory),
      avgRenderTime: this.calculateAverage(this.renderTimeHistory),
      avgUpdateTime: this.calculateAverage(this.updateTimeHistory)
    };
  }

  private calculateAverage(values: number[]): number {
    if (values.length === 0) return 0;
    return values.reduce((sum, value) => sum + value, 0) / values.length;
  }

  getPerformanceReport(): string {
    const avg = this.getAverageMetrics();
    const current = this.metrics;
    
    return `
Performance Report:
==================
Current FPS: ${current.fps.toFixed(1)}
Average FPS: ${avg.avgFPS.toFixed(1)}
Current Frame Time: ${current.frameTime.toFixed(2)}ms
Average Frame Time: ${avg.avgFrameTime.toFixed(2)}ms
Current Render Time: ${current.renderTime.toFixed(2)}ms
Average Render Time: ${avg.avgRenderTime.toFixed(2)}ms
Memory Usage: ${(current.memoryUsage / 1024 / 1024).toFixed(2)}MB
Visible Objects: ${current.visibleObjects}
Total Objects: ${current.totalObjects}
Culled Objects: ${current.culledObjects}
LOD Level: ${current.lodLevel}
Cache Hit Rate: ${current.cacheHitRate.toFixed(1)}%
    `.trim();
  }

  reset(): void {
    this.frameCount = 0;
    this.fpsHistory = [];
    this.frameTimeHistory = [];
    this.renderTimeHistory = [];
    this.updateTimeHistory = [];
    this.metrics = {
      fps: 0,
      frameTime: 0,
      renderTime: 0,
      updateTime: 0,
      memoryUsage: 0,
      visibleObjects: 0,
      totalObjects: 0,
      culledObjects: 0,
      lodLevel: 0,
      cacheHitRate: 0
    };
  }
}

// Global performance monitor instance
export const globalPerformanceMonitor = new PerformanceMonitor();
