export interface MemoryStats {
  totalAllocated: number;
  totalReleased: number;
  currentUsage: number;
  peakUsage: number;
  gcCount: number;
  lastGcTime: number;
}

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  size: number;
}

export interface CacheOptions {
  maxSize: number; // Maximum cache size in bytes
  maxEntries: number; // Maximum number of entries
  ttl: number; // Time to live in milliseconds
  cleanupInterval: number; // Cleanup interval in milliseconds
}

export class AdvancedMemoryManager {
  private memoryStats: MemoryStats = {
    totalAllocated: 0,
    totalReleased: 0,
    currentUsage: 0,
    peakUsage: 0,
    gcCount: 0,
    lastGcTime: 0
  };

  private caches = new Map<string, LRUCache<any>>();
  private cleanupInterval?: NodeJS.Timeout;
  private gcThreshold = 50 * 1024 * 1024; // 50MB threshold for manual GC
  private lastGcCheck = 0;

  constructor() {
    this.startMemoryMonitoring();
  }

  private startMemoryMonitoring(): void {
    // Monitor memory usage every 5 seconds
    setInterval(() => {
      this.updateMemoryStats();
      this.checkGarbageCollection();
    }, 5000);
  }

  private updateMemoryStats(): void {
    if ((performance as any).memory) {
      const memory = (performance as any).memory;
      this.memoryStats.currentUsage = memory.usedJSHeapSize;
      this.memoryStats.peakUsage = Math.max(this.memoryStats.peakUsage, memory.usedJSHeapSize);
    }
  }

  private checkGarbageCollection(): void {
    const now = Date.now();
    
    // Check if we should trigger manual GC
    if (this.memoryStats.currentUsage > this.gcThreshold && 
        now - this.lastGcCheck > 10000) { // At least 10 seconds between checks
      
      this.triggerGarbageCollection();
      this.lastGcCheck = now;
    }
  }

  private triggerGarbageCollection(): void {
    // Force garbage collection if available (Chrome DevTools)
    if ((window as any).gc) {
      (window as any).gc();
      this.memoryStats.gcCount++;
      this.memoryStats.lastGcTime = Date.now();
    }

    // Clean up all caches
    for (const cache of this.caches.values()) {
      cache.cleanup();
    }
  }

  createCache<T>(name: string, options: Partial<CacheOptions> = {}): LRUCache<T> {
    const defaultOptions: CacheOptions = {
      maxSize: 10 * 1024 * 1024, // 10MB
      maxEntries: 1000,
      ttl: 5 * 60 * 1000, // 5 minutes
      cleanupInterval: 60 * 1000 // 1 minute
    };

    const finalOptions = { ...defaultOptions, ...options };
    const cache = new LRUCache<T>(finalOptions);
    this.caches.set(name, cache);
    
    return cache;
  }

  getCache<T>(name: string): LRUCache<T> | undefined {
    return this.caches.get(name);
  }

  removeCache(name: string): boolean {
    const cache = this.caches.get(name);
    if (cache) {
      cache.clear();
      return this.caches.delete(name);
    }
    return false;
  }

  clearAllCaches(): void {
    for (const cache of this.caches.values()) {
      cache.clear();
    }
    this.caches.clear();
  }

  getMemoryStats(): MemoryStats {
    this.updateMemoryStats();
    return { ...this.memoryStats };
  }

  getCacheStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    for (const [name, cache] of this.caches) {
      stats[name] = cache.getStats();
    }
    
    return stats;
  }

  // Memory allocation tracking
  trackAllocation(size: number): void {
    this.memoryStats.totalAllocated += size;
    this.memoryStats.currentUsage += size;
    this.memoryStats.peakUsage = Math.max(this.memoryStats.peakUsage, this.memoryStats.currentUsage);
  }

  trackDeallocation(size: number): void {
    this.memoryStats.totalReleased += size;
    this.memoryStats.currentUsage = Math.max(0, this.memoryStats.currentUsage - size);
  }

  // Cleanup
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clearAllCaches();
  }
}

export class LRUCache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private options: CacheOptions;
  private currentSize = 0;
  private cleanupInterval?: NodeJS.Timeout;

  constructor(options: CacheOptions) {
    this.options = options;
    this.startCleanup();
  }

  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, this.options.cleanupInterval);
  }

  set(key: string, value: T, size: number = 0): void {
    const now = Date.now();
    
    // Remove existing entry if it exists
    if (this.cache.has(key)) {
      const existing = this.cache.get(key)!;
      this.currentSize -= existing.size;
    }

    // Check if we need to make space
    while ((this.cache.size >= this.options.maxEntries || 
            this.currentSize + size > this.options.maxSize) && 
           this.cache.size > 0) {
      this.evictLRU();
    }

    // Add new entry
    const entry: CacheEntry<T> = {
      data: value,
      timestamp: now,
      accessCount: 0,
      lastAccessed: now,
      size
    };

    this.cache.set(key, entry);
    this.currentSize += size;
  }

  get(key: string): T | undefined {
    const entry = this.cache.get(key);
    if (!entry) return undefined;

    const now = Date.now();
    
    // Check if entry has expired
    if (now - entry.timestamp > this.options.ttl) {
      this.delete(key);
      return undefined;
    }

    // Update access statistics
    entry.lastAccessed = now;
    entry.accessCount++;

    // Move to end (most recently used)
    this.cache.delete(key);
    this.cache.set(key, entry);

    return entry.data;
  }

  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    // Check if expired
    if (Date.now() - entry.timestamp > this.options.ttl) {
      this.delete(key);
      return false;
    }

    return true;
  }

  delete(key: string): boolean {
    const entry = this.cache.get(key);
    if (entry) {
      this.currentSize -= entry.size;
      return this.cache.delete(key);
    }
    return false;
  }

  clear(): void {
    this.cache.clear();
    this.currentSize = 0;
  }

  private evictLRU(): void {
    // Find least recently used entry
    let lruKey: string | null = null;
    let lruTime = Infinity;

    for (const [key, entry] of this.cache) {
      if (entry.lastAccessed < lruTime) {
        lruTime = entry.lastAccessed;
        lruKey = key;
      }
    }

    if (lruKey) {
      this.delete(lruKey);
    }
  }

  cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    // Find expired entries
    for (const [key, entry] of this.cache) {
      if (now - entry.timestamp > this.options.ttl) {
        keysToDelete.push(key);
      }
    }

    // Remove expired entries
    for (const key of keysToDelete) {
      this.delete(key);
    }
  }

  getStats(): {
    size: number;
    entries: number;
    maxEntries: number;
    maxSize: number;
    currentSize: number;
    hitRate: number;
  } {
    let totalAccesses = 0;
    let totalHits = 0;

    for (const entry of this.cache.values()) {
      totalAccesses += entry.accessCount;
      if (entry.accessCount > 0) totalHits++;
    }

    return {
      size: this.cache.size,
      entries: this.cache.size,
      maxEntries: this.options.maxEntries,
      maxSize: this.options.maxSize,
      currentSize: this.currentSize,
      hitRate: totalAccesses > 0 ? (totalHits / totalAccesses) * 100 : 0
    };
  }

  // Get all keys sorted by access frequency
  getKeysByFrequency(): string[] {
    const entries = Array.from(this.cache.entries());
    entries.sort((a, b) => b[1].accessCount - a[1].accessCount);
    return entries.map(([key]) => key);
  }

  // Get cache efficiency metrics
  getEfficiencyMetrics(): {
    averageAccessCount: number;
    oldestEntry: number;
    newestEntry: number;
    memoryEfficiency: number;
  } {
    if (this.cache.size === 0) {
      return {
        averageAccessCount: 0,
        oldestEntry: 0,
        newestEntry: 0,
        memoryEfficiency: 0
      };
    }

    let totalAccesses = 0;
    let oldestTime = Infinity;
    let newestTime = 0;

    for (const entry of this.cache.values()) {
      totalAccesses += entry.accessCount;
      oldestTime = Math.min(oldestTime, entry.timestamp);
      newestTime = Math.max(newestTime, entry.timestamp);
    }

    return {
      averageAccessCount: totalAccesses / this.cache.size,
      oldestEntry: Date.now() - oldestTime,
      newestEntry: Date.now() - newestTime,
      memoryEfficiency: (this.currentSize / this.options.maxSize) * 100
    };
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }
}

// Global memory manager instance
export const globalMemoryManager = new AdvancedMemoryManager();
