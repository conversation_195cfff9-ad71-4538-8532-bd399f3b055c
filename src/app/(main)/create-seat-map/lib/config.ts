export const LAYOUT_HEIGHT = 500;
export const LAYOUT_WIDTH = 500;
export const SEAT_SIZE = 20;
export const SEAT_GAP = 8;
export const SHAPE_PADDING = 10;

// Performance Configuration
export const VIEWPORT_BUFFER = 100; // Extra pixels around viewport for smooth scrolling
export const MAX_VISIBLE_SEATS = 10000; // Maximum seats to render at once
export const LOD_THRESHOLD_1 = 0.5; // Below this zoom, show simplified seats
export const LOD_THRESHOLD_2 = 0.2; // Below this zoom, show section outlines only
export const BATCH_SIZE = 1000; // Number of seats to process in each batch
export const QUADTREE_MAX_OBJECTS = 50; // Maximum objects per quadtree node
export const QUADTREE_MAX_LEVELS = 8; // Maximum quadtree depth

// Seat States
export const SEAT_STATES = {
  AVAILABLE: 'available',
  SELECTED: 'selected',
  DISABLED: 'disabled',
  SOLD: 'sold',
  RESERVED: 'reserved'
} as const;

// Rendering Layers
export const RENDER_LAYERS = {
  BACKGROUND: 0,
  SECTIONS: 1,
  SEATS: 2,
  LABELS: 3,
  SELECTION: 4,
  UI: 5
} as const;
